// @ts-check
import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import { getAllLanguageCodes, DEFAULT_LANGUAGE } from './src/config/languages.ts';

// https://astro.build/config
export default defineConfig({
  integrations: [tailwind()],
  site: 'https://flashimage.fun',

  // 静态发布优化
  output: 'static',

  // 构建优化
  build: {
    inlineStylesheets: 'auto',
  },

  // 多语言配置 - 使用统一的语言配置
  i18n: {
    defaultLocale: DEFAULT_LANGUAGE,
    locales: getAllLanguageCodes(),
    routing: {
      prefixDefaultLocale: true,
      redirectToDefaultLocale: false
    }
  },

  // Vite 配置优化
  vite: {
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            // 将大型库分离到单独的 chunk
            vendor: ['astro']
          }
        }
      }
    }
  }
});
