#!/usr/bin/env node
const fs = require('fs');
const path = require('path');

const i18nDir = path.join(__dirname, '..', 'src', 'i18n');
const referenceLang = process.env.REF_LANG || 'en';
const targetLang = process.env.TARGET_LANG || 'es';

function readJson(p) {
  return JSON.parse(fs.readFileSync(p, 'utf-8'));
}

function getAllStringKeyPaths(obj, prefix = '') {
  const keys = [];
  for (const k of Object.keys(obj)) {
    const v = obj[k];
    const curr = prefix ? `${prefix}.${k}` : k;
    if (v && typeof v === 'object' && !Array.isArray(v)) {
      keys.push(...getAllStringKeyPaths(v, curr));
    } else {
      keys.push(curr);
    }
  }
  return keys;
}

function getValueByPath(obj, pathStr) {
  return pathStr.split('.').reduce((o, k) => (o && typeof o === 'object') ? o[k] : undefined, obj);
}

// Add allowlists for keys allowed to be identical to reference (brand names, emails, numbers)
const allowedEqualKeys = new Set([
  'site.title',
  'site.contact',
  'comparison.hero.title',
  'comparison.table.headers.gemini',
  'comparison.table.headers.imagen'
]);

const allowedEqualKeyRegexes = [
  /\.number$/,      // numeric-like display strings such as 10M+, 99.9%
  /\.count$/,       // any counters
  /\.url$/,         // links
  /\.email$/,       // emails
  /\.winner$/       // winner labels often product names, same across langs
];

function isAllowedEqualKey(key) {
  if (allowedEqualKeys.has(key)) return true;
  return allowedEqualKeyRegexes.some((re) => re.test(key));
}

function buildQualityReport(translations, langCode, reference) {
  const report = {
    language: langCode,
    totalKeys: 0,
    translatedKeys: 0,
    emptyKeys: 0,
    completionPercentage: 0,
    issues: []
  };

  const refKeys = getAllStringKeyPaths(reference);
  report.totalKeys = refKeys.length;

  const brandNames = ['Flash Image Fun', 'Gemini 2.5 Flash Image', 'Google AI Studio'];
  const urlPattern = /https?:\/\/[^\s]+/g;

  for (const key of refKeys) {
    const refVal = getValueByPath(reference, key);
    const val = getValueByPath(translations, key);

    if (!val || (typeof val === 'string' && val.trim() === '')) {
      report.emptyKeys++;
      report.issues.push({ type: 'empty', key, message: 'Translation is empty or missing', severity: 'error' });
      continue;
    }

    if (val === refVal) {
      if (langCode !== referenceLang) {
        if (isAllowedEqualKey(key)) {
          report.translatedKeys++;
        } else {
          report.issues.push({ type: 'untranslated', key, message: 'Content appears to be untranslated (same as reference)', severity: 'warning' });
        }
      } else {
        report.translatedKeys++;
      }
      continue;
    }

    report.translatedKeys++;
    if (typeof val === 'string' && typeof refVal === 'string') {
      const suspiciousBrands = brandNames.filter(b => refVal.includes(b) && !val.includes(b));
      if (suspiciousBrands.length > 0) {
        report.issues.push({ type: 'suspicious', key, message: `Brand names may be incorrectly translated: ${suspiciousBrands.join(', ')}` , severity: 'warning'});
      }
      const refUrls = refVal.match(urlPattern) || [];
      const valUrls = val.match(urlPattern) || [];
      if (refUrls.length !== valUrls.length) {
        report.issues.push({ type: 'formatting', key, message: 'URL count mismatch between reference and translation', severity: 'error' });
      }
    }
  }

  report.completionPercentage = Math.round((report.translatedKeys / report.totalKeys) * 100);
  return report;
}

function generateMarkdown(reports) {
  let out = '# Translation Quality Report\n\n';
  const total = reports.length;
  const avg = reports.reduce((s, r) => s + r.completionPercentage, 0) / total;
  out += `## Summary\n`;
  out += `- Total Languages: ${total}\n`;
  out += `- Average Completion: ${avg.toFixed(1)}%\n\n`;
  out += `## Language Status\n\n`;
  out += `| Language | Completion | Translated | Total | Issues |\n`;
  out += `|----------|------------|------------|-------|--------|\n`;
  for (const r of reports) {
    const e = r.issues.filter(i => i.severity === 'error').length;
    const w = r.issues.filter(i => i.severity === 'warning').length;
    out += `| ${r.language.toUpperCase()} | ${r.completionPercentage}% | ${r.translatedKeys} | ${r.totalKeys} | ${e}E, ${w}W |\n`;
  }
  out += '\n';
  for (const r of reports) {
    if (r.issues.length === 0) continue;
    out += `## ${r.language.toUpperCase()} Issues\n\n`;
    const errors = r.issues.filter(i => i.severity === 'error');
    const warnings = r.issues.filter(i => i.severity === 'warning');
    if (errors.length) {
      out += `### Errors (${errors.length})\n`;
      errors.forEach(i => { out += `- **${i.key}**: ${i.message}\n`; });
      out += '\n';
    }
    if (warnings.length) {
      out += `### Warnings (${warnings.length})\n`;
      warnings.forEach(i => { out += `- **${i.key}**: ${i.message}\n`; });
      out += '\n';
    }
  }
  return out;
}

(function main(){
  const refPath = path.join(i18nDir, `${referenceLang}.json`);
  const tgtPath = path.join(i18nDir, `${targetLang}.json`);
  if (!fs.existsSync(refPath) || !fs.existsSync(tgtPath)) {
    console.error('Missing translation files:', { refPath, tgtPath });
    process.exit(1);
  }
  const reference = readJson(refPath);
  const target = readJson(tgtPath);
  const report = buildQualityReport(target, targetLang, reference);
  const md = generateMarkdown([report]);
  console.log(md);
})();