#!/usr/bin/env node
/*
 * Translation structure validator
 * Validates that all translation JSON files in src/i18n keep the same key paths as en.json
 */
const fs = require('fs');
const path = require('path');

const i18nDir = path.join(__dirname, '..', 'src', 'i18n');
const referenceFile = 'en.json';

function getKeyPaths(obj, prefix = '') {
  const keys = [];
  for (const key in obj) {
    if (!Object.prototype.hasOwnProperty.call(obj, key)) continue;
    const currentPath = prefix ? `${prefix}.${key}` : key;
    const val = obj[key];
    if (val && typeof val === 'object' && !Array.isArray(val)) {
      keys.push(...getKeyPaths(val, currentPath));
    } else {
      keys.push(currentPath);
    }
  }
  return keys;
}

function getValueByPath(obj, pathStr) {
  return pathStr.split('.').reduce((o, k) => (o ? o[k] : undefined), obj);
}

function validateFile(filePath, referencePath) {
  const result = {
    isValid: true,
    errors: [],
    warnings: [],
    missingKeys: [],
    extraKeys: [],
    emptyKeys: []
  };
  try {
    const refData = JSON.parse(fs.readFileSync(referencePath, 'utf-8'));
    const refKeys = getKeyPaths(refData);

    const data = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
    const keys = getKeyPaths(data);

    result.missingKeys = refKeys.filter(k => !keys.includes(k));
    result.extraKeys = keys.filter(k => !refKeys.includes(k));

    if (result.missingKeys.length > 0) {
      result.isValid = false;
      result.errors.push(`Missing keys: ${result.missingKeys.length}`);
    }
    if (result.extraKeys.length > 0) {
      result.warnings.push(`Extra keys: ${result.extraKeys.length}`);
    }

    // Empty values check
    result.emptyKeys = keys.filter(k => {
      const v = getValueByPath(data, k);
      return v === null || v === undefined || (typeof v === 'string' && v.trim() === '');
    });
    if (result.emptyKeys.length > 0) {
      result.warnings.push(`Empty values: ${result.emptyKeys.length}`);
    }
  } catch (e) {
    result.isValid = false;
    result.errors.push(`Parse/IO error: ${e.message}`);
  }
  return result;
}

function main() {
  const referencePath = path.join(i18nDir, referenceFile);
  if (!fs.existsSync(referencePath)) {
    console.error(`[ERROR] Reference file not found: ${referencePath}`);
    process.exit(1);
  }

  const files = fs.readdirSync(i18nDir).filter(f => f.endsWith('.json'));
  const targets = files.filter(f => f !== referenceFile);

  let overallValid = true;

  console.log('=== Translation Structure Validation ===');
  console.log(`Reference: ${referenceFile}`);
  console.log(`Total target languages: ${targets.length}`);
  console.log('----------------------------------------');

  for (const file of targets) {
    const filePath = path.join(i18nDir, file);
    const r = validateFile(filePath, referencePath);
    const lang = path.basename(file, '.json');
    const status = r.isValid ? 'VALID' : 'INVALID';
    console.log(`${lang}: ${status}`);
    if (!r.isValid) {
      overallValid = false;
    }
    if (r.errors.length) {
      console.log(`  Errors: ${r.errors.join('; ')}`);
      // Show up to 5 sample missing keys
      if (r.missingKeys.length) {
        console.log('  Missing sample:', r.missingKeys.slice(0, 5));
      }
    }
    if (r.warnings.length) {
      console.log(`  Warnings: ${r.warnings.join('; ')}`);
    }
  }

  console.log('----------------------------------------');
  console.log(`Overall: ${overallValid ? '✅ All valid' : '❌ Issues detected'}`);
  process.exit(overallValid ? 0 : 2);
}

if (require.main === module) {
  main();
}