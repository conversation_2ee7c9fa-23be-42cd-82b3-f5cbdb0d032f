---
// 移动端优化组件
// 包含触摸优化、视口优化、性能优化等
---

<!-- 移动端视口优化 -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
<meta name="format-detection" content="telephone=no" />
<meta name="mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

<!-- PWA 支持 -->
<meta name="theme-color" content="#1f2937" />
<meta name="msapplication-navbutton-color" content="#1f2937" />
<meta name="apple-mobile-web-app-title" content="Flash Image Fun" />

<!-- 移动端优化样式 -->
<style>
  /* 触摸优化 */
  @media (hover: none) and (pointer: coarse) {
    /* 移动设备特定样式 */
    button, a, [role="button"] {
      min-height: 44px; /* iOS 推荐的最小触摸目标 */
      min-width: 44px;
    }
    
    /* 移除 hover 效果，使用 active 状态 */
    .hover\:scale-105:hover {
      transform: none;
    }
    
    .hover\:scale-105:active {
      transform: scale(0.98);
      transition: transform 0.1s ease-out;
    }
    
    /* 优化按钮点击反馈 */
    button:active, [role="button"]:active {
      background-color: rgba(59, 130, 246, 0.1);
      transform: scale(0.98);
    }
  }
  
  /* 移动端导航优化 */
  @media (max-width: 768px) {
    /* 语言切换器移动端优化 */
    #language-menu {
      position: fixed !important;
      top: auto !important;
      bottom: 20px !important;
      left: 50% !important;
      right: auto !important;
      transform: translateX(-50%) translateY(100px) !important;
      width: calc(100vw - 40px) !important;
      max-width: 320px !important;
    }
    
    #language-menu.opacity-100 {
      transform: translateX(-50%) translateY(0) !important;
    }
    
    /* 移动端菜单项优化 */
    .language-link {
      padding: 16px 20px !important;
      font-size: 16px !important;
    }
    
    /* 移动端头部优化 */
    header {
      padding-left: 16px !important;
      padding-right: 16px !important;
    }
    
    /* 移动端按钮优化 */
    #language-button {
      padding: 12px 16px !important;
      font-size: 16px !important;
    }
  }
  
  /* 超小屏幕优化 */
  @media (max-width: 480px) {
    /* 进一步优化小屏幕体验 */
    .text-4xl {
      font-size: 2rem !important;
    }
    
    .text-6xl {
      font-size: 3rem !important;
    }
    
    /* 移动端间距优化 */
    .px-4 {
      padding-left: 16px !important;
      padding-right: 16px !important;
    }
    
    .py-20 {
      padding-top: 3rem !important;
      padding-bottom: 3rem !important;
    }
  }
  
  /* 横屏模式优化 */
  @media (orientation: landscape) and (max-height: 500px) {
    .pt-32 {
      padding-top: 2rem !important;
    }
    
    .pb-20 {
      padding-bottom: 2rem !important;
    }
  }
  
  /* 高 DPI 屏幕优化 */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* 高分辨率屏幕的图像优化 */
    img {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
  }
  
  /* 减少动画的用户偏好 */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  /* 暗色模式优化 */
  @media (prefers-color-scheme: dark) {
    /* 确保在系统暗色模式下的一致性 */
    :root {
      color-scheme: dark;
    }
  }
  
  /* 滚动优化 */
  html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
  
  /* iOS Safari 优化 */
  @supports (-webkit-touch-callout: none) {
    /* iOS 特定优化 */
    body {
      -webkit-text-size-adjust: 100%;
      -webkit-font-smoothing: antialiased;
    }
    
    /* 修复 iOS Safari 的 100vh 问题 */
    .min-h-screen {
      min-height: -webkit-fill-available;
    }
  }
</style>

<script>
  // 移动端优化 JavaScript
  document.addEventListener('DOMContentLoaded', function() {
    // 检测移动设备
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    if (isMobile || isTouchDevice) {
      document.body.classList.add('mobile-device');
      
      // 移动端特定优化
      optimizeForMobile();
    }
    
    // 视口高度修复（iOS Safari）
    function setViewportHeight() {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    }
    
    setViewportHeight();
    window.addEventListener('resize', setViewportHeight);
    window.addEventListener('orientationchange', function() {
      setTimeout(setViewportHeight, 100);
    });
    
    // 移动端优化函数
    function optimizeForMobile() {
      // 优化触摸滚动
      document.body.style.webkitOverflowScrolling = 'touch';
      
      // 防止双击缩放
      let lastTouchEnd = 0;
      document.addEventListener('touchend', function(event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
      
      // 优化语言切换器在移动端的行为
      const languageButton = document.getElementById('language-button');
      const languageMenu = document.getElementById('language-menu');
      
      if (languageButton && languageMenu) {
        // 移动端点击优化
        languageButton.addEventListener('touchstart', function() {
          this.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
        });
        
        languageButton.addEventListener('touchend', function() {
          setTimeout(() => {
            this.style.backgroundColor = '';
          }, 150);
        });
        
        // 移动端菜单关闭优化
        document.addEventListener('touchstart', function(e) {
          if (!languageButton.contains(e.target) && !languageMenu.contains(e.target)) {
            if (languageMenu.classList.contains('opacity-100')) {
              // 触发关闭动画
              const event = new Event('click');
              document.dispatchEvent(event);
            }
          }
        });
      }
      
      // 性能优化：延迟加载非关键资源
      if ('requestIdleCallback' in window) {
        requestIdleCallback(function() {
          // 在浏览器空闲时执行非关键任务
          preloadCriticalResources();
        });
      } else {
        setTimeout(preloadCriticalResources, 100);
      }
    }
    
    // 预加载关键资源
    function preloadCriticalResources() {
      // 预加载字体
      const fontLink = document.createElement('link');
      fontLink.rel = 'preload';
      fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap';
      fontLink.as = 'style';
      fontLink.onload = function() {
        this.onload = null;
        this.rel = 'stylesheet';
      };
      document.head.appendChild(fontLink);
    }
    
    // 网络状态优化
    if ('connection' in navigator) {
      const connection = navigator.connection;
      
      // 根据网络状况调整体验
      if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
        // 慢网络优化
        document.body.classList.add('slow-network');
        
        // 减少动画
        const style = document.createElement('style');
        style.textContent = `
          .slow-network * {
            animation-duration: 0.1s !important;
            transition-duration: 0.1s !important;
          }
        `;
        document.head.appendChild(style);
      }
    }
    
    // 电池状态优化
    if ('getBattery' in navigator) {
      navigator.getBattery().then(function(battery) {
        if (battery.level < 0.2 && !battery.charging) {
          // 低电量模式
          document.body.classList.add('low-battery');
          
          // 减少动画和效果
          const style = document.createElement('style');
          style.textContent = `
            .low-battery * {
              animation: none !important;
              transition: none !important;
            }
          `;
          document.head.appendChild(style);
        }
      });
    }
  });
</script>
