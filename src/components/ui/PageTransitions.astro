---
// 页面过渡动画组件
// 为语言切换和页面导航提供平滑的过渡效果
---

<style>
  /* 页面加载动画 */
  .page-transition {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }
  
  .page-transition.loaded {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* 语言切换过渡动画 */
  .language-switching {
    position: relative;
    overflow: hidden;
  }
  
  .language-switching::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    animation: languageSwitch 1.5s ease-in-out;
    z-index: 1;
  }
  
  @keyframes languageSwitch {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }
  
  /* 内容淡入动画 */
  .fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* 分段动画 */
  .stagger-animation > * {
    opacity: 0;
    transform: translateY(20px);
    animation: staggerFadeIn 0.6s ease-out forwards;
  }
  
  .stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
  .stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
  .stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
  .stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
  .stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
  .stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }
  
  @keyframes staggerFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* 加载指示器 */
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(17, 24, 39, 0.9);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
  }
  
  .loading-overlay.hidden {
    opacity: 0;
    visibility: hidden;
  }
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(59, 130, 246, 0.3);
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* 进度条 */
  .progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    z-index: 10000;
    transition: width 0.3s ease-out;
  }
  
  /* 页面切换动画 */
  .page-enter {
    animation: pageEnter 0.5s ease-out forwards;
  }
  
  .page-exit {
    animation: pageExit 0.3s ease-in forwards;
  }
  
  @keyframes pageEnter {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes pageExit {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(-20px);
    }
  }
  
  /* 移动端优化动画 */
  @media (max-width: 768px) {
    .page-transition {
      transform: translateY(10px);
    }
    
    .stagger-animation > * {
      transform: translateY(10px);
    }
    
    @keyframes pageEnter {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    @keyframes pageExit {
      from {
        opacity: 1;
        transform: translateY(0);
      }
      to {
        opacity: 0;
        transform: translateY(-10px);
      }
    }
  }
  
  /* 减少动画偏好 */
  @media (prefers-reduced-motion: reduce) {
    .page-transition,
    .fade-in,
    .stagger-animation > *,
    .page-enter,
    .page-exit {
      animation: none !important;
      transition: none !important;
    }
    
    .page-transition {
      opacity: 1;
      transform: none;
    }
    
    .stagger-animation > * {
      opacity: 1;
      transform: none;
    }
  }
</style>

<script>
  // 页面过渡动画控制器
  class PageTransitionManager {
    constructor() {
      this.isTransitioning = false;
      this.progressBar = null;
      this.loadingOverlay = null;
      this.init();
    }
    
    init() {
      this.createProgressBar();
      this.createLoadingOverlay();
      this.setupPageTransitions();
      this.setupLanguageSwitching();
      this.setupIntersectionObserver();
    }
    
    createProgressBar() {
      this.progressBar = document.createElement('div');
      this.progressBar.className = 'progress-bar';
      document.body.appendChild(this.progressBar);
    }
    
    createLoadingOverlay() {
      this.loadingOverlay = document.createElement('div');
      this.loadingOverlay.className = 'loading-overlay';
      this.loadingOverlay.innerHTML = `
        <div class="text-center">
          <div class="loading-spinner mb-4"></div>
          <p class="text-gray-300 text-sm">Switching languages…</p>
        </div>
      `;
      document.body.appendChild(this.loadingOverlay);
      
      // 初始隐藏
      setTimeout(() => {
        this.loadingOverlay.classList.add('hidden');
      }, 100);
    }
    
    setupPageTransitions() {
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', () => {
        const mainContent = document.querySelector('main');
        if (mainContent) {
          mainContent.classList.add('page-transition');
          setTimeout(() => {
            mainContent.classList.add('loaded');
          }, 100);
        }
        
        // 分段动画
        const staggerElements = document.querySelectorAll('.stagger-animation');
        staggerElements.forEach(element => {
          this.observeElement(element);
        });
      });
    }
    
    setupLanguageSwitching() {
      // 监听语言切换链接
      document.addEventListener('click', (e) => {
        const languageLink = e.target.closest('.language-link');
        if (languageLink && !languageLink.classList.contains('bg-gradient-to-r')) {
          this.handleLanguageSwitch(e, languageLink);
        }
      });
    }
    
    handleLanguageSwitch(event, link) {
      if (this.isTransitioning) return;
      
      this.isTransitioning = true;
      
      // 显示加载覆盖层
      this.loadingOverlay.classList.remove('hidden');
      
      // 显示进度条
      this.showProgress();
      
      // 添加切换动画类
      document.body.classList.add('language-switching');
      
      // 模拟加载进度
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 90) progress = 90;
        this.updateProgress(progress);
      }, 100);
      
      // 延迟导航以显示动画
      setTimeout(() => {
        clearInterval(progressInterval);
        this.updateProgress(100);
        
        setTimeout(() => {
          window.location.href = link.href;
        }, 200);
      }, 800);
    }
    
    showProgress() {
      this.progressBar.style.width = '0%';
      this.progressBar.style.opacity = '1';
    }
    
    updateProgress(percent) {
      this.progressBar.style.width = `${percent}%`;
    }
    
    hideProgress() {
      setTimeout(() => {
        this.progressBar.style.opacity = '0';
        setTimeout(() => {
          this.progressBar.style.width = '0%';
        }, 300);
      }, 500);
    }
    
    setupIntersectionObserver() {
      // 创建交叉观察器用于触发动画
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('fade-in');
            observer.unobserve(entry.target);
          }
        });
      }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      });
      
      // 观察所有需要动画的元素
      const animatedElements = document.querySelectorAll('.animate-on-scroll');
      animatedElements.forEach(el => observer.observe(el));
    }
    
    observeElement(element) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('stagger-animation');
            observer.unobserve(entry.target);
          }
        });
      }, { threshold: 0.2 });
      
      observer.observe(element);
    }
  }
  
  // 初始化页面过渡管理器
  document.addEventListener('DOMContentLoaded', () => {
    new PageTransitionManager();
  });
  
  // 页面卸载时的清理
  window.addEventListener('beforeunload', () => {
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
      progressBar.style.width = '100%';
    }
  });
</script>
