---
import {
  getLangFromUrl,
  languages,
  removeLanguagePrefix,
  getLocalizedPath,
} from "../../i18n";
import { getEnabledLanguages, getLanguagesByRegion } from "../../config/languages";

const currentLang = getLangFromUrl(Astro.url);
const currentPath = Astro.url.pathname;

// Remove language prefix from path to get base path
const basePath = removeLanguagePrefix(currentPath);

// Generate language links grouped by region
const enabledLanguages = getEnabledLanguages();
const languagesByRegion = getLanguagesByRegion();



// Group enabled languages by region
const groupedLanguages = Object.entries(languagesByRegion)
  .map(([region, langs]) => ({
    region,
    languages: langs
      .filter(langConfig => langConfig.enabled)
      .map(langConfig => ({
        code: langConfig.code,
        name: langConfig.name,
        href: getLocalizedPath(basePath, langConfig.code as keyof typeof languages),
        active: langConfig.code === currentLang,
        flag: langConfig.flag
      }))
  }))
  .filter(group => group.languages.length > 0)
  .sort((a, b) => {
    // 优先显示当前语言所在的地区
    const currentRegion = enabledLanguages.find(l => l.code === currentLang)?.region;
    if (a.region === currentRegion) return -1;
    if (b.region === currentRegion) return 1;
    return a.region.localeCompare(b.region);
  });
---

<!-- Enhanced Language Switcher with Smooth Animations -->
<div class="relative inline-block text-left">
  <button
    id="language-button"
    type="button"
    class="inline-flex items-center justify-center rounded-xl border border-gray-600/50 shadow-lg px-4 py-2.5 bg-gray-800/80 backdrop-blur-sm text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700/80 hover:border-gray-500/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-300 hover:scale-105 hover:shadow-xl"
    aria-expanded="false"
    aria-haspopup="true"
  >
    <span class="text-lg mr-2 animate-pulse">🌐</span>
    <span class="font-medium">{languages[currentLang]}</span>
    <svg
      id="chevron-icon"
      class="w-4 h-4 ml-2 transition-transform duration-300"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M19 9l-7 7-7-7"></path>
    </svg>
  </button>

  <div
    id="language-menu"
    class="absolute right-0 z-50 mt-3 w-64 opacity-0 invisible transform scale-95 translate-y-2 transition-all duration-300 ease-out"
  >
    <div
      class="bg-gray-800/95 backdrop-blur-sm border border-gray-700/50 rounded-xl shadow-2xl ring-1 ring-white/10 max-h-96 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800"
    >
      <div class="py-2" role="menu" aria-orientation="vertical">
        {
          groupedLanguages.map(({ region, languages: regionLanguages }) => (
            <div class="region-group">
              {/* Region Header */}
              <div class="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider border-b border-gray-700/30 bg-gray-750/50">
                {region}
              </div>

              {/* Languages in this region */}
              {regionLanguages.map(({ code, name, href, active, flag }) => (
                <a
                  href={href}
                  class={`language-link group flex items-center px-4 py-3 text-sm transition-all duration-200 relative overflow-hidden ${
                    active
                      ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium shadow-lg"
                      : "text-gray-300 hover:text-white hover:bg-gray-700/50"
                  }`}
                  role="menuitem"
                  data-lang={code}
                >
                  <span class="text-lg mr-3 transition-transform duration-200 group-hover:scale-110">
                    {flag}
                  </span>
                  <span class="flex-1 font-medium">{name}</span>
                  {active ? (
                    <svg
                      class="w-4 h-4 ml-2 text-green-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      class="w-4 h-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  )}
                  {!active && (
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 transform -translate-x-full transition-transform duration-300 group-hover:translate-x-0" />
                  )}
                </a>
              ))}
            </div>
          ))
        }
      </div>
    </div>
  </div>
</div>

<script>
  // Enhanced Language Switcher with Smart Positioning and Smooth Animations
  document.addEventListener("DOMContentLoaded", function () {
    const button = document.getElementById("language-button") as HTMLButtonElement;
    const menu = document.getElementById("language-menu") as HTMLDivElement;
    const chevron = document.getElementById("chevron-icon") as SVGElement;
    const languageLinks = document.querySelectorAll(".language-link") as NodeListOf<HTMLAnchorElement>;

    if (!button || !menu || !chevron) return;

    let isOpen = false;

    // Smart positioning function
    function updateMenuPosition() {
      const buttonRect = button.getBoundingClientRect();
      const menuHeight = 384; // max-h-96 = 384px
      const viewportHeight = window.innerHeight;
      const spaceBelow = viewportHeight - buttonRect.bottom;
      const spaceAbove = buttonRect.top;

      // Reset positioning classes
      menu.classList.remove("top-full", "bottom-full", "mt-3", "mb-3");

      if (spaceBelow >= menuHeight || spaceBelow >= spaceAbove) {
        // Show below (default)
        menu.classList.add("top-full", "mt-3");
      } else {
        // Show above
        menu.classList.add("bottom-full", "mb-3");
      }
    }

    // Toggle dropdown with smooth animations and smart positioning
    function toggleDropdown() {
      isOpen = !isOpen;

      if (isOpen) {
        // Update position before showing
        updateMenuPosition();

        // Open animation
        menu.classList.remove(
          "opacity-0",
          "invisible",
          "scale-95",
          "translate-y-2",
        );
        menu.classList.add(
          "opacity-100",
          "visible",
          "scale-100",
          "translate-y-0",
        );
        chevron.style.transform = "rotate(180deg)";
        button.setAttribute("aria-expanded", "true");
      } else {
        // Close animation
        menu.classList.remove(
          "opacity-100",
          "visible",
          "scale-100",
          "translate-y-0",
        );
        menu.classList.add(
          "opacity-0",
          "invisible",
          "scale-95",
          "translate-y-2",
        );
        chevron.style.transform = "rotate(0deg)";
        button.setAttribute("aria-expanded", "false");
      }
    }

    // Button click handler
    button.addEventListener("click", function (e) {
      e.stopPropagation();
      toggleDropdown();
    });

    // Close dropdown when clicking outside
    document.addEventListener("click", function (event) {
      const target = event.target as Node;
      if (!button.contains(target) && !menu.contains(target)) {
        if (isOpen) {
          toggleDropdown();
        }
      }
    });

    // Update position on window resize
    window.addEventListener("resize", function () {
      if (isOpen) {
        updateMenuPosition();
      }
    });

    // Keyboard navigation
    button.addEventListener("keydown", function (e) {
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        toggleDropdown();
      } else if (e.key === "Escape" && isOpen) {
        toggleDropdown();
      }
    });

    // Language link click handlers with preference saving
    languageLinks.forEach((link) => {
      link.addEventListener("click", function () {
        const langCode = this.getAttribute("data-lang");
        if (langCode) {
          // Save language preference
          localStorage.setItem("preferred-language", langCode);

          // Add loading state
          this.style.opacity = "0.7";
          this.style.transform = "scale(0.98)";

          // Show loading indicator
          const loadingSpinner = document.createElement("div");
          loadingSpinner.className =
            "inline-block w-4 h-4 ml-2 animate-spin rounded-full border-2 border-current border-t-transparent";
          this.appendChild(loadingSpinner);
        }
      });


    });

    // Close dropdown on Escape key
    document.addEventListener("keydown", function (e) {
      if (e.key === "Escape" && isOpen) {
        toggleDropdown();
        button.focus();
      }
    });
  });
</script>

<style>
  /* Custom scrollbar styles for the language menu */
  #language-menu .bg-gray-800\/95 {
    scrollbar-width: thin;
    scrollbar-color: #4b5563 #1f2937;
  }

  #language-menu .bg-gray-800\/95::-webkit-scrollbar {
    width: 6px;
  }

  #language-menu .bg-gray-800\/95::-webkit-scrollbar-track {
    background: #1f2937;
    border-radius: 3px;
  }

  #language-menu .bg-gray-800\/95::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  #language-menu .bg-gray-800\/95::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }

  /* Region header styling */
  .region-group:not(:first-child) .px-4.py-2.text-xs {
    border-top: 1px solid rgba(75, 85, 99, 0.3);
  }

  /* Smooth scroll behavior */
  #language-menu .bg-gray-800\/95 {
    scroll-behavior: smooth;
  }

  /* 防止横向滚动条 */
  .language-link {
    box-sizing: border-box;
    min-width: 0;
    overflow: hidden;
  }

  /* 优化hover效果，避免布局偏移 */
  .language-link:not(.bg-gradient-to-r):hover {
    padding-left: 20px !important;
    transition: padding-left 0.2s ease;
  }

  .language-link:not(.bg-gradient-to-r) {
    padding-left: 16px;
    transition: padding-left 0.2s ease;
  }
</style>
