---
import {
  getLangFromUrl,
  languages,
  removeLanguagePrefix,
  getLocalizedPath,
} from "../../i18n";
import { getEnabledLanguages } from "../../config/languages";

const lang = getLangFromUrl(Astro.url);
const currentPath = Astro.url.pathname;

// Remove language prefix from path to get base path
const basePath = removeLanguagePrefix(currentPath);

// Generate language links
// 使用统一的语言配置获取标志
const enabledLanguages = getEnabledLanguages();

function getLanguageFlag(code: string): string {
  const languageConfig = enabledLanguages.find((lang) => lang.code === code);
  return languageConfig?.flag || "🌐";
}

const languageLinks = Object.entries(languages).map(([code, name]) => {
  const href = getLocalizedPath(basePath, code as keyof typeof languages);

  return {
    code,
    name,
    href,
    active: code === lang,
  };
});
---

<!-- Enhanced Language Switcher with Smooth Animations -->
<div class="relative inline-block text-left">
  <button
    id="language-button"
    type="button"
    class="inline-flex items-center justify-center rounded-xl border border-gray-600/50 shadow-lg px-4 py-2.5 bg-gray-800/80 backdrop-blur-sm text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700/80 hover:border-gray-500/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-300 hover:scale-105 hover:shadow-xl"
    aria-expanded="false"
    aria-haspopup="true"
  >
    <span class="text-lg mr-2 animate-pulse">🌐</span>
    <span class="font-medium">{languages[lang]}</span>
    <svg
      id="chevron-icon"
      class="w-4 h-4 ml-2 transition-transform duration-300"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M19 9l-7 7-7-7"></path>
    </svg>
  </button>

  <div
    id="language-menu"
    class="absolute right-0 z-50 mt-3 w-52 opacity-0 invisible transform scale-95 translate-y-2 transition-all duration-300 ease-out"
  >
    <div
      class="bg-gray-800/95 backdrop-blur-sm border border-gray-700/50 rounded-xl shadow-2xl ring-1 ring-white/10"
    >
      <div class="py-2" role="menu" aria-orientation="vertical">
        {
          languageLinks.map(({ code, name, href, active }) => (
            <a
              href={href}
              class={`language-link group flex items-center px-4 py-3 text-sm transition-all duration-200 relative overflow-hidden ${
                active
                  ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium shadow-lg"
                  : "text-gray-300 hover:text-white hover:bg-gray-700/50"
              }`}
              role="menuitem"
              data-lang={code}
            >
              <span class="text-lg mr-3 transition-transform duration-200 group-hover:scale-110">
                {getLanguageFlag(code)}
              </span>
              <span class="flex-1 font-medium">{name}</span>
              {active ? (
                <svg
                  class="w-4 h-4 ml-2 text-green-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              ) : (
                <svg
                  class="w-4 h-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              )}
              {!active && (
                <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 transform -translate-x-full transition-transform duration-300 group-hover:translate-x-0" />
              )}
            </a>
          ))
        }
      </div>
    </div>
  </div>
</div>

<script>
  // Enhanced Language Switcher with Smooth Animations and Preference Memory
  document.addEventListener("DOMContentLoaded", function () {
    const button = document.getElementById("language-button");
    const menu = document.getElementById("language-menu");
    const chevron = document.getElementById("chevron-icon");
    const languageLinks = document.querySelectorAll(".language-link");

    if (!button || !menu || !chevron) return;

    let isOpen = false;

    // Toggle dropdown with smooth animations
    function toggleDropdown() {
      isOpen = !isOpen;

      if (isOpen) {
        // Open animation
        menu.classList.remove(
          "opacity-0",
          "invisible",
          "scale-95",
          "translate-y-2",
        );
        menu.classList.add(
          "opacity-100",
          "visible",
          "scale-100",
          "translate-y-0",
        );
        chevron.style.transform = "rotate(180deg)";
        button.setAttribute("aria-expanded", "true");
      } else {
        // Close animation
        menu.classList.remove(
          "opacity-100",
          "visible",
          "scale-100",
          "translate-y-0",
        );
        menu.classList.add(
          "opacity-0",
          "invisible",
          "scale-95",
          "translate-y-2",
        );
        chevron.style.transform = "rotate(0deg)";
        button.setAttribute("aria-expanded", "false");
      }
    }

    // Button click handler
    button.addEventListener("click", function (e) {
      e.stopPropagation();
      toggleDropdown();
    });

    // Close dropdown when clicking outside
    document.addEventListener("click", function (event) {
      if (!button.contains(event.target) && !menu.contains(event.target)) {
        if (isOpen) {
          toggleDropdown();
        }
      }
    });

    // Keyboard navigation
    button.addEventListener("keydown", function (e) {
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        toggleDropdown();
      } else if (e.key === "Escape" && isOpen) {
        toggleDropdown();
      }
    });

    // Language link click handlers with preference saving
    languageLinks.forEach((link) => {
      link.addEventListener("click", function (e) {
        const langCode = this.getAttribute("data-lang");
        if (langCode) {
          // Save language preference
          localStorage.setItem("preferred-language", langCode);

          // Add loading state
          this.style.opacity = "0.7";
          this.style.transform = "scale(0.98)";

          // Show loading indicator
          const loadingSpinner = document.createElement("div");
          loadingSpinner.className =
            "inline-block w-4 h-4 ml-2 animate-spin rounded-full border-2 border-current border-t-transparent";
          this.appendChild(loadingSpinner);
        }
      });

      // Hover effects
      link.addEventListener("mouseenter", function () {
        if (!this.classList.contains("bg-gradient-to-r")) {
          this.style.transform = "translateX(4px)";
        }
      });

      link.addEventListener("mouseleave", function () {
        if (!this.classList.contains("bg-gradient-to-r")) {
          this.style.transform = "translateX(0)";
        }
      });
    });

    // Close dropdown on Escape key
    document.addEventListener("keydown", function (e) {
      if (e.key === "Escape" && isOpen) {
        toggleDropdown();
        button.focus();
      }
    });
  });
</script>
