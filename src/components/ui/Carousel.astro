---
export interface Props {
  images: Array<{
    src: string;
    alt: string;
    title?: string;
    description?: string;
  }>;
  autoplay?: boolean;
  interval?: number;
}

const { images, autoplay = true, interval = 5000 } = Astro.props;
---

<div class="relative w-full max-w-4xl mx-auto">
  <!-- Carousel container -->
  <div class="relative overflow-hidden rounded-2xl shadow-2xl bg-white">
    <!-- Image container -->
    <div class="carousel-track flex transition-transform duration-500 ease-in-out" id="carousel-track">
      {images.map((image, index) => (
        <div class="carousel-slide w-full flex-shrink-0 relative">
          <div class="aspect-video bg-gray-100 flex items-center justify-center">
            <!-- Placeholder for now - will be replaced with actual images -->
            <div class="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
              <div class="text-center">
                <svg class="w-16 h-16 text-blue-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{image.title}</h3>
                <p class="text-gray-600 text-sm px-4">{image.description}</p>
              </div>
            </div>
          </div>
          
          <!-- Caption overlay -->
          {(image.title || image.description) && (
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
              {image.title && (
                <h3 class="text-white text-lg font-semibold mb-2">{image.title}</h3>
              )}
              {image.description && (
                <p class="text-white/90 text-sm">{image.description}</p>
              )}
            </div>
          )}
        </div>
      ))}
    </div>

    <!-- Navigation arrows -->
    <button 
      class="carousel-prev absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-110"
      aria-label="Previous image"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>
    
    <button 
      class="carousel-next absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-110"
      aria-label="Next image"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>

    <!-- Dots indicator -->
    <div class="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
      {images.map((_, index) => (
        <button 
          class={`carousel-dot w-3 h-3 rounded-full transition-all duration-200 ${index === 0 ? 'bg-white' : 'bg-white/50 hover:bg-white/75'}`}
          data-slide={index}
          aria-label={`Go to slide ${index + 1}`}
        />
      ))}
    </div>
  </div>
</div>

<script define:vars={{ autoplay, interval }}>
  class Carousel {
    constructor(element) {
      this.carousel = element;
      this.track = element.querySelector('.carousel-track');
      this.slides = element.querySelectorAll('.carousel-slide');
      this.prevBtn = element.querySelector('.carousel-prev');
      this.nextBtn = element.querySelector('.carousel-next');
      this.dots = element.querySelectorAll('.carousel-dot');
      
      this.currentSlide = 0;
      this.totalSlides = this.slides.length;
      this.autoplayInterval = null;
      
      this.init();
    }
    
    init() {
      // Add event listeners
      this.prevBtn?.addEventListener('click', () => this.prevSlide());
      this.nextBtn?.addEventListener('click', () => this.nextSlide());
      
      this.dots.forEach((dot, index) => {
        dot.addEventListener('click', () => this.goToSlide(index));
      });
      
      // Start autoplay if enabled
      if (autoplay) {
        this.startAutoplay();
        
        // Pause on hover
        this.carousel.addEventListener('mouseenter', () => this.stopAutoplay());
        this.carousel.addEventListener('mouseleave', () => this.startAutoplay());
      }
      
      // Initialize first slide
      this.updateSlide();
    }
    
    nextSlide() {
      this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
      this.updateSlide();
    }
    
    prevSlide() {
      this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
      this.updateSlide();
    }
    
    goToSlide(index) {
      this.currentSlide = index;
      this.updateSlide();
    }
    
    updateSlide() {
      // Update track position
      const translateX = -this.currentSlide * 100;
      this.track.style.transform = `translateX(${translateX}%)`;
      
      // Update dots
      this.dots.forEach((dot, index) => {
        if (index === this.currentSlide) {
          dot.classList.remove('bg-white/50', 'hover:bg-white/75');
          dot.classList.add('bg-white');
        } else {
          dot.classList.remove('bg-white');
          dot.classList.add('bg-white/50', 'hover:bg-white/75');
        }
      });
    }
    
    startAutoplay() {
      if (autoplay) {
        this.autoplayInterval = setInterval(() => {
          this.nextSlide();
        }, interval);
      }
    }
    
    stopAutoplay() {
      if (this.autoplayInterval) {
        clearInterval(this.autoplayInterval);
        this.autoplayInterval = null;
      }
    }
  }
  
  // Initialize carousel when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    const carouselElement = document.querySelector('.relative.w-full.max-w-4xl');
    if (carouselElement) {
      new Carousel(carouselElement);
    }
  });
</script>
