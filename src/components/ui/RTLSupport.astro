---
/**
 * RTL语言支持组件
 * 为阿拉伯语、希伯来语等RTL语言提供特殊样式和布局支持
 */

import { isRTLLanguage } from '../../config/languages';

interface Props {
  lang: string;
}

const { lang } = Astro.props;
const isRTL = isRTLLanguage(lang);
---

{isRTL && (
  <style is:global>
    /* RTL语言全局样式 */
    html[lang="ar"], html[lang="he"] {
      direction: rtl;
    }

    /* RTL布局调整 */
    [lang="ar"] .container, [lang="he"] .container {
      text-align: right;
    }

    /* RTL导航调整 */
    [lang="ar"] .nav-links, [lang="he"] .nav-links {
      flex-direction: row-reverse;
    }

    [lang="ar"] .nav-links li, [lang="he"] .nav-links li {
      margin-left: 0;
      margin-right: 1.5rem;
    }

    /* RTL按钮调整 */
    [lang="ar"] .btn, [lang="he"] .btn {
      text-align: center;
    }

    /* RTL图标调整 */
    [lang="ar"] .icon-arrow, [lang="he"] .icon-arrow {
      transform: scaleX(-1);
    }

    /* RTL语言切换器调整 */
    [lang="ar"] #language-menu, [lang="he"] #language-menu {
      right: auto;
      left: 0;
    }

    /* RTL卡片布局调整 */
    [lang="ar"] .feature-card, [lang="he"] .feature-card {
      text-align: right;
    }

    [lang="ar"] .feature-card .icon, [lang="he"] .feature-card .icon {
      margin-left: 0;
      margin-right: 1rem;
    }

    /* RTL统计数据调整 */
    [lang="ar"] .stats-grid, [lang="he"] .stats-grid {
      direction: rtl;
    }

    /* RTL证言调整 */
    [lang="ar"] .testimonial-card, [lang="he"] .testimonial-card {
      text-align: right;
    }

    [lang="ar"] .testimonial-avatar, [lang="he"] .testimonial-avatar {
      margin-left: 0;
      margin-right: 1rem;
    }

    /* RTL页脚调整 */
    [lang="ar"] .footer-links, [lang="he"] .footer-links {
      text-align: right;
    }

    [lang="ar"] .footer-section, [lang="he"] .footer-section {
      text-align: right;
    }

    /* RTL表单调整 */
    [lang="ar"] .form-group, [lang="he"] .form-group {
      text-align: right;
    }

    [lang="ar"] .form-input, [lang="he"] .form-input {
      text-align: right;
      padding-left: 1rem;
      padding-right: 0.75rem;
    }

    /* RTL面包屑调整 */
    [lang="ar"] .breadcrumb, [lang="he"] .breadcrumb {
      direction: rtl;
    }

    [lang="ar"] .breadcrumb-separator, [lang="he"] .breadcrumb-separator {
      transform: scaleX(-1);
    }

    /* RTL代码块调整（保持LTR） */
    [lang="ar"] pre, [lang="he"] pre,
    [lang="ar"] code, [lang="he"] code {
      direction: ltr;
      text-align: left;
    }

    /* RTL数字和英文文本保持LTR */
    [lang="ar"] .number, [lang="he"] .number,
    [lang="ar"] .english-text, [lang="he"] .english-text {
      direction: ltr;
      display: inline-block;
    }

    /* RTL响应式调整 */
    @media (max-width: 768px) {
      [lang="ar"] .mobile-menu, [lang="he"] .mobile-menu {
        right: auto;
        left: 0;
      }

      [lang="ar"] .mobile-nav-toggle, [lang="he"] .mobile-nav-toggle {
        right: auto;
        left: 1rem;
      }
    }

    /* RTL动画调整 */
    [lang="ar"] .slide-in-left, [lang="he"] .slide-in-left {
      animation-name: slideInRight;
    }

    [lang="ar"] .slide-in-right, [lang="he"] .slide-in-right {
      animation-name: slideInLeft;
    }

    /* RTL特定字体优化 */
    [lang="ar"] {
      font-family: 'Noto Sans Arabic', 'Arial', sans-serif;
      line-height: 1.8;
    }

    [lang="he"] {
      font-family: 'Noto Sans Hebrew', 'Arial', sans-serif;
      line-height: 1.7;
    }

    /* RTL文本对齐微调 */
    [lang="ar"] h1, [lang="ar"] h2, [lang="ar"] h3,
    [lang="he"] h1, [lang="he"] h2, [lang="he"] h3 {
      text-align: right;
    }

    [lang="ar"] p, [lang="he"] p {
      text-align: right;
      margin-right: 0;
      margin-left: auto;
    }

    /* RTL列表调整 */
    [lang="ar"] ul, [lang="ar"] ol,
    [lang="he"] ul, [lang="he"] ol {
      padding-right: 1.5rem;
      padding-left: 0;
    }

    [lang="ar"] li, [lang="he"] li {
      text-align: right;
    }

    /* RTL表格调整 */
    [lang="ar"] table, [lang="he"] table {
      direction: rtl;
    }

    [lang="ar"] th, [lang="ar"] td,
    [lang="he"] th, [lang="he"] td {
      text-align: right;
    }

    /* RTL工具提示调整 */
    [lang="ar"] .tooltip, [lang="he"] .tooltip {
      direction: rtl;
      text-align: right;
    }

    /* RTL模态框调整 */
    [lang="ar"] .modal-content, [lang="he"] .modal-content {
      text-align: right;
    }

    [lang="ar"] .modal-close, [lang="he"] .modal-close {
      right: auto;
      left: 1rem;
    }
  </style>
)}

<!-- RTL语言特定的字体加载 -->
{isRTL && (
  <link
    rel="preconnect"
    href="https://fonts.googleapis.com"
  />
  <link
    rel="preconnect"
    href="https://fonts.gstatic.com"
    crossorigin="anonymous"
  />
  {lang === 'ar' && (
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  )}
  {lang === 'he' && (
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+Hebrew:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  )}
)}

<!-- RTL语言的特殊meta标签 -->
{isRTL && (
  <meta name="format-detection" content="telephone=no" />
  <meta name="text-direction" content="rtl" />
)}
