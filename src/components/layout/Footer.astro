---
import { getLangFromUrl, useTranslations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const siteTitle = t('site.title');
const siteDescription = t('site.description');
---

<footer class="bg-black border-t border-gray-800">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid md:grid-cols-4 gap-8">
      <!-- Brand section -->
      <div class="md:col-span-2">
        <div class="flex items-center space-x-3 mb-4">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
            </svg>
          </div>
          <span class="text-xl font-bold text-white">{siteTitle}</span>
        </div>
        <p class="text-gray-400 mb-6 max-w-md">
          {siteDescription}
        </p>
        <!-- Contact email -->
        <div class="flex items-center space-x-3">
          <div class="flex items-center space-x-2 text-gray-400">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <a href={`mailto:${t('site.contact')}`} class="hover:text-white transition-colors duration-200">
              {t('site.contact')}
            </a>
          </div>
        </div>
      </div>

      <!-- Quick links -->
      <div>
        <h3 class="text-lg font-semibold mb-4 text-white">{t('footer.quickLinks')}</h3>
        <ul class="space-y-2">
          <li><a href={`/${lang}#features`} class="text-gray-400 hover:text-white transition-colors duration-200">{t('footer.features')}</a></li>
          <li><a href={`/${lang}#testimonials`} class="text-gray-400 hover:text-white transition-colors duration-200">{t('footer.testimonials')}</a></li>
          <li><a href={`/${lang}/gemini-vs-imagen`} class="text-gray-400 hover:text-white transition-colors duration-200">{t('footer.modelComparison')}</a></li>
        </ul>
      </div>

      <!-- Resources -->
      <div>
        <h3 class="text-lg font-semibold mb-4 text-white">{t('footer.resources')}</h3>
        <ul class="space-y-2">
          <li><a href={`/${lang}/developer-guide`} class="text-gray-400 hover:text-white transition-colors duration-200">{t('footer.helpCenter')}</a></li>
          <li><a href="https://developers.googleblog.com/en/introducing-gemini-2-5-flash-image/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors duration-200">{t('footer.apiDocs')}</a></li>
          <li><a href={`/${lang}/nano-banana-truth`} class="text-gray-400 hover:text-white transition-colors duration-200">{t('footer.community')}</a></li>
        </ul>
      </div>
    </div>

    <!-- Bottom section -->
    <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
      <div class="text-gray-500 text-sm">
        © 2024 {siteTitle}. {t('footer.copyright')}
      </div>
      <div class="flex space-x-6 mt-4 md:mt-0">
        <a href={`/${lang}/privacy-policy`} class="text-gray-500 hover:text-white text-sm transition-colors duration-200">{t('footer.privacyPolicy')}</a>
        <a href={`/${lang}/terms-of-service`} class="text-gray-500 hover:text-white text-sm transition-colors duration-200">{t('footer.termsOfService')}</a>
        <a href={`/${lang}/cookie-policy`} class="text-gray-500 hover:text-white text-sm transition-colors duration-200">{t('footer.cookiePolicy')}</a>
      </div>
    </div>
  </div>
</footer>
