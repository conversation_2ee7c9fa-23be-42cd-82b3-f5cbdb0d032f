---
import { getLangFromUrl, useTranslations, getLocalizedPath } from '../../i18n';
import LanguageSwitcher from '../ui/LanguageSwitcher.astro';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Navigation configuration
const siteTitle = t('site.title');
const navItems = [
  { label: t('nav.features'), href: getLocalizedPath('#features', lang) },
  { label: t('nav.developerGuide'), href: getLocalizedPath('developer-guide', lang) },
  { label: t('nav.modelComparison'), href: getLocalizedPath('gemini-vs-imagen', lang) },
  { label: t('nav.nanoBananaTruth'), href: getLocalizedPath('nano-banana-truth', lang) },
  { label: t('nav.testimonials'), href: getLocalizedPath('#testimonials', lang) }
];
const ctaPrimary = t('hero.cta.primary');
---

<header class="fixed top-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md border-b border-gray-800">
  <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex items-center">
        <a href={getLocalizedPath('', lang)} class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
            </svg>
          </div>
          <span class="text-xl font-bold text-white">{siteTitle}</span>
        </a>
      </div>

      <!-- Navigation Links -->
      <div class="hidden md:flex items-center space-x-8">
        {navItems.map((item) => (
          <a
            href={item.href}
            class="text-gray-300 hover:text-white transition-colors duration-200 relative group"
          >
            {item.label}
            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 group-hover:w-full transition-all duration-300"></span>
          </a>
        ))}
      </div>

      <!-- Language Switcher & CTA Button -->
      <div class="flex items-center space-x-4">
        <LanguageSwitcher />
        <a
          href={getLocalizedPath('#try-now', lang)}
          class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2.5 rounded-full font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          {ctaPrimary}
        </a>
      </div>

      <!-- Mobile menu button -->
      <div class="md:hidden">
        <button
          type="button"
          class="text-gray-300 hover:text-white focus:outline-none focus:text-white"
          id="mobile-menu-button"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile menu -->
    <div class="md:hidden hidden" id="mobile-menu">
      <div class="px-2 pt-2 pb-3 space-y-1 bg-gray-900 border-t border-gray-800">
        {navItems.map((item) => (
          <a
            href={item.href}
            class="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-200"
          >
            {item.label}
          </a>
        ))}
      </div>
    </div>
  </nav>
</header>

<script>
  // Mobile menu toggle
  document.addEventListener('DOMContentLoaded', () => {
    const button = document.getElementById('mobile-menu-button');
    const menu = document.getElementById('mobile-menu');
    
    button?.addEventListener('click', () => {
      menu?.classList.toggle('hidden');
    });
  });
</script>
