---
import { getLangFromUrl, useTranslations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Get carousel data completely from translation system
const carouselSections = t('carousel.sections');
---

<section class="py-20 bg-black">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    {carouselSections.map((section: any, sectionIndex: number) => (
      <div class="mb-32 last:mb-0">
        <!-- Section header -->
        <div class="text-center mb-16">
          <h2 class="text-4xl sm:text-5xl font-bold text-white mb-6">
            {section.title}
          </h2>
          <p class="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            {section.description}
          </p>
        </div>

        <!-- Image carousel -->
        <div class="relative">
          <!-- Carousel container -->
          <div class="overflow-hidden rounded-3xl">
            <div 
              class="flex transition-transform duration-500 ease-in-out carousel-track"
              id={`carousel-${sectionIndex}`}
            >
              {section.images.map((image: any) => (
                <div class="w-full flex-shrink-0 relative carousel-slide">
                  <!-- Real image with fallback -->
                  <div class="aspect-video relative overflow-hidden rounded-2xl">
                    <img
                      src={image.src}
                      alt={image.alt}
                      class="w-full h-full object-cover"
                      loading="lazy"
                      onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                    />

                    <!-- Fallback placeholder (hidden by default) -->
                    <div class="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center" style="display: none;">
                      <!-- Background pattern -->
                      <div class="absolute inset-0 opacity-10">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"></div>
                        <div class="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-400/10 rounded-full blur-xl"></div>
                        <div class="absolute bottom-1/4 right-1/4 w-32 h-32 bg-purple-400/10 rounded-full blur-xl"></div>
                      </div>

                      <!-- Fallback content -->
                      <div class="relative z-10 text-center p-8">
                        <div class="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                          <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-3">{image.title}</h3>
                        <p class="text-gray-300 text-lg">{image.description}</p>
                      </div>
                    </div>

                    <!-- Image overlay with title, description, and prompt -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent flex items-end">
                      <div class="p-8 text-white w-full">
                        <h3 class="text-2xl font-bold mb-2">{image.title}</h3>
                        <p class="text-gray-200 text-lg mb-4">{image.description}</p>
                        {image.prompt && (
                          <div class="bg-gray-900/70 backdrop-blur-sm rounded-lg p-4 border border-gray-600/30">
                            <p class="text-xs text-gray-400 mb-2 font-medium uppercase tracking-wide">Prompt</p>
                            <p class="text-sm text-gray-100 italic leading-relaxed">"{image.prompt}"</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <!-- Navigation arrows -->
          <button 
            class="absolute left-6 top-1/2 -translate-y-1/2 bg-black/80 hover:bg-black text-white rounded-full p-4 shadow-2xl transition-all duration-200 hover:scale-110 z-10 carousel-prev"
            data-carousel={sectionIndex}
            aria-label="Previous image"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          <button 
            class="absolute right-6 top-1/2 -translate-y-1/2 bg-black/80 hover:bg-black text-white rounded-full p-4 shadow-2xl transition-all duration-200 hover:scale-110 z-10 carousel-next"
            data-carousel={sectionIndex}
            aria-label="Next image"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>

          <!-- Dots indicator -->
          <div class="flex justify-center mt-8 space-x-3">
            {section.images.map((_: any, dotIndex: number) => (
              <button 
                class={`w-3 h-3 rounded-full transition-all duration-200 carousel-dot ${dotIndex === 0 ? 'bg-white' : 'bg-white/30 hover:bg-white/60'}`}
                data-carousel={sectionIndex}
                data-slide={dotIndex}
                aria-label={`Go to slide ${dotIndex + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    ))}
  </div>
</section>

<script>
  class ImageCarousel {
    constructor() {
      this.carousels = [];
      this.init();
    }
    
    init() {
      // Initialize all carousels
      document.querySelectorAll('[id^="carousel-"]').forEach((carousel, index) => {
        this.carousels[index] = {
          element: carousel,
          currentSlide: 0,
          totalSlides: carousel.querySelectorAll('.carousel-slide').length,
          autoplayInterval: null
        };
        
        this.setupEventListeners(index);
        this.startAutoplay(index);
      });
    }
    
    setupEventListeners(carouselIndex) {
      // Previous button
      document.querySelector(`[data-carousel="${carouselIndex}"].carousel-prev`)?.addEventListener('click', () => {
        this.prevSlide(carouselIndex);
      });
      
      // Next button
      document.querySelector(`[data-carousel="${carouselIndex}"].carousel-next`)?.addEventListener('click', () => {
        this.nextSlide(carouselIndex);
      });
      
      // Dots
      document.querySelectorAll(`[data-carousel="${carouselIndex}"].carousel-dot`).forEach((dot, dotIndex) => {
        dot.addEventListener('click', () => {
          this.goToSlide(carouselIndex, dotIndex);
        });
      });
      
      // Pause autoplay on hover
      const carouselContainer = this.carousels[carouselIndex].element.closest('.relative');
      carouselContainer?.addEventListener('mouseenter', () => this.stopAutoplay(carouselIndex));
      carouselContainer?.addEventListener('mouseleave', () => this.startAutoplay(carouselIndex));
    }
    
    nextSlide(carouselIndex) {
      const carousel = this.carousels[carouselIndex];
      carousel.currentSlide = (carousel.currentSlide + 1) % carousel.totalSlides;
      this.updateSlide(carouselIndex);
    }
    
    prevSlide(carouselIndex) {
      const carousel = this.carousels[carouselIndex];
      carousel.currentSlide = (carousel.currentSlide - 1 + carousel.totalSlides) % carousel.totalSlides;
      this.updateSlide(carouselIndex);
    }
    
    goToSlide(carouselIndex, slideIndex) {
      this.carousels[carouselIndex].currentSlide = slideIndex;
      this.updateSlide(carouselIndex);
    }
    
    updateSlide(carouselIndex) {
      const carousel = this.carousels[carouselIndex];
      const translateX = -carousel.currentSlide * 100;
      carousel.element.style.transform = `translateX(${translateX}%)`;
      
      // Update dots
      document.querySelectorAll(`[data-carousel="${carouselIndex}"].carousel-dot`).forEach((dot, index) => {
        if (index === carousel.currentSlide) {
          dot.classList.remove('bg-white/30', 'hover:bg-white/60');
          dot.classList.add('bg-white');
        } else {
          dot.classList.remove('bg-white');
          dot.classList.add('bg-white/30', 'hover:bg-white/60');
        }
      });
    }
    
    startAutoplay(carouselIndex) {
      const carousel = this.carousels[carouselIndex];
      carousel.autoplayInterval = setInterval(() => {
        this.nextSlide(carouselIndex);
      }, 5000);
    }
    
    stopAutoplay(carouselIndex) {
      const carousel = this.carousels[carouselIndex];
      if (carousel.autoplayInterval) {
        clearInterval(carousel.autoplayInterval);
        carousel.autoplayInterval = null;
      }
    }
  }
  
  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new ImageCarousel();
  });
</script>
