---
import { getLangFromUrl, useTranslations, translations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// User testimonials section - 强制使用英文数据作为回退
const testimonials = translations.en?.testimonials?.items || [
  {
    name: "<PERSON>",
    role: "Creative Director",
    company: "Design Studio Pro",
    avatar: "👩‍🎨",
    content: "Gemini 2.5 Flash Image has revolutionized our creative workflow.",
    rating: 5
  },
  {
    name: "<PERSON>",
    role: "Marketing Manager",
    company: "TechStart Inc",
    avatar: "👨‍💼",
    content: "We now use this for all our marketing campaigns.",
    rating: 5
  },
  {
    name: "<PERSON>",
    role: "Freelance Illustrator",
    company: "Independent Artist",
    avatar: "👩‍🎨",
    content: "As a freelancer, this tool has dramatically expanded my capabilities.",
    rating: 5
  }
];
---

<section id="testimonials" class="py-20 bg-black relative overflow-hidden">
  <!-- Background decoration -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-l from-purple-500/10 to-transparent rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 left-0 w-96 h-96 bg-gradient-to-r from-blue-500/10 to-transparent rounded-full blur-3xl"></div>
  </div>

  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section header -->
    <div class="text-center mb-16">
      <h2 class="text-4xl sm:text-5xl font-bold text-white mb-6">
        <span class="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
          {t('testimonials.title')}
        </span>
      </h2>
      <p class="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
        {t('testimonials.subtitle')}
      </p>
    </div>

    <!-- Testimonials grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {testimonials.map((testimonial, index) => (
        <div class="group">
          <div class="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-2xl p-8 hover:bg-gray-900/70 transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-purple-500/10 h-full flex flex-col">
            <!-- Rating stars -->
            <div class="flex mb-4">
              {Array.from({ length: testimonial.rating }, (_, i) => (
                <svg key={i} class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>

            <!-- Content -->
            <blockquote class="text-gray-300 leading-relaxed mb-6 flex-grow">
              "{testimonial.content}"
            </blockquote>

            <!-- Author info -->
            <div class="flex items-center">
              <div class="text-3xl mr-4 group-hover:scale-110 transition-transform duration-300">
                {testimonial.avatar}
              </div>
              <div>
                <div class="font-semibold text-white group-hover:text-purple-400 transition-colors duration-300">
                  {testimonial.name}
                </div>
                <div class="text-sm text-gray-400">
                  {testimonial.role}
                </div>
                <div class="text-sm text-gray-500">
                  {testimonial.company}
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>

    <!-- Bottom CTA -->
    <div class="text-center mt-16">
      <div class="inline-flex flex-col sm:flex-row items-center gap-4">
        <div class="flex -space-x-2">
          {testimonials.slice(0, 4).map((testimonial, index) => (
            <div key={index} class="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-semibold border-2 border-gray-900">
              {testimonial.avatar}
            </div>
          ))}
          <div class="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 text-sm font-semibold border-2 border-gray-900">
            +2K
          </div>
        </div>
        <div class="text-gray-400">
          {t('testimonials.joinText')} <span class="text-white font-semibold">2,000+</span> {t('testimonials.satisfiedCreators')}
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-slide-in-up {
    animation: slideInUp 0.6s ease-out;
  }
</style>
