---
import { getLangFromUrl, useTranslations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Try Gemini section migrated from original Gemini 2.5 Flash Image page
const tryOptions = t('tryGemini.options');
---

<section class="py-20 bg-gradient-to-b from-gray-800/50 to-gray-900/80">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-6xl font-bold mb-6">
        <span class="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
          {t('tryGemini.title')}
        </span>
      </h2>
      <p class="text-xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
        {t('tryGemini.subtitle')}
      </p>
    </div>

    <!-- Try Options Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto mb-16">
      {tryOptions.map((option) => (
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-3xl p-8 hover:bg-gray-800/70 transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/20">
          <!-- Option Content -->
          <div class="text-center">
            <h3 class="text-2xl font-bold text-white mb-4">
              {option.title}
            </h3>
            <p class="text-gray-400 leading-relaxed mb-8">
              {option.description}
            </p>
            
            <!-- CTA Button -->
            <a
              href={option.url}
              target="_blank"
              rel="noopener noreferrer"
              class={`inline-flex items-center justify-center px-8 py-4 font-semibold rounded-full transition-all duration-300 transform hover:scale-105 text-lg ${
                option.isPrimary 
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 shadow-2xl hover:shadow-blue-500/25' 
                  : 'border-2 border-gray-600 text-gray-300 hover:border-gray-400 hover:text-white'
              }`}
            >
              {option.buttonText}
              <svg class="ml-3 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </a>
          </div>
        </div>
      ))}
    </div>

    <!-- Additional Information -->
    <div class="text-center">
      <div class="bg-gray-800/30 backdrop-blur-sm border border-gray-700/30 rounded-2xl p-8 max-w-4xl mx-auto">
        <h3 class="text-xl font-semibold text-white mb-4">
          {t('tryGemini.additionalInfo.title')}
        </h3>
        <p class="text-gray-400 leading-relaxed mb-6">
          {t('tryGemini.additionalInfo.description')}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
          {t('tryGemini.additionalInfo.features').map((feature) => (
            <div class="flex items-center text-gray-500 text-sm">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              {feature}
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
</section>
