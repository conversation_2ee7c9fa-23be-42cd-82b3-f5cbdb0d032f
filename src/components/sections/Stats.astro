---
import { getLangFromUrl, useTranslations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Statistics section for the website
const stats = [
  {
    number: t('stats.items.imagesGenerated.number'),
    label: t('stats.items.imagesGenerated.label'),
    description: t('stats.items.imagesGenerated.description'),
    icon: "🎨"
  },
  {
    number: t('stats.items.activeUsers.number'),
    label: t('stats.items.activeUsers.label'),
    description: t('stats.items.activeUsers.description'),
    icon: "👥"
  },
  {
    number: t('stats.items.uptime.number'),
    label: t('stats.items.uptime.label'),
    description: t('stats.items.uptime.description'),
    icon: "⚡"
  },
  {
    number: t('stats.items.languages.number'),
    label: t('stats.items.languages.label'),
    description: t('stats.items.languages.description'),
    icon: "🌍"
  }
];
---

<section class="py-20 bg-gradient-to-b from-gray-900 to-black relative overflow-hidden">
  <!-- Background decoration -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute top-0 left-1/4 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 right-1/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl"></div>
  </div>

  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section header -->
    <div class="text-center mb-16">
      <h2 class="text-4xl sm:text-5xl font-bold text-white mb-6">
        <span class="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
          {t('stats.title')}
        </span>
      </h2>
      <p class="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
        {t('stats.subtitle')}
      </p>
    </div>

    <!-- Stats grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      {stats.map((stat, index) => (
        <div class="text-center group">
          <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:bg-gray-800/70 transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/10">
            <!-- Icon -->
            <div class="text-5xl mb-4 group-hover:scale-110 transition-transform duration-300">
              {stat.icon}
            </div>
            
            <!-- Number -->
            <div class="text-4xl sm:text-5xl font-bold text-white mb-2 group-hover:text-blue-400 transition-colors duration-300">
              {stat.number}
            </div>
            
            <!-- Label -->
            <div class="text-xl font-semibold text-gray-300 mb-3">
              {stat.label}
            </div>
            
            <!-- Description -->
            <div class="text-sm text-gray-500 leading-relaxed">
              {stat.description}
            </div>
          </div>
        </div>
      ))}
    </div>

    <!-- Call to action -->
    <div class="text-center mt-16">
      <div class="inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-blue-300 font-medium">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
        {t('stats.realTimeNote')}
      </div>
    </div>
  </div>
</section>

<style>
  @keyframes countUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-count-up {
    animation: countUp 0.8s ease-out;
  }
</style>
