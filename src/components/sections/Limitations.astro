---
import { getLangFromUrl, useTranslations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const limitations = [
  {
    title: t('limitations.items.0.title'),
    description: t('limitations.items.0.description'),
    icon: "fact"
  },
  {
    title: t('limitations.items.1.title'),
    description: t('limitations.items.1.description'),
    icon: "character"
  }
];
---

<section class="py-20 bg-gray-900/30">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
        {t('limitations.title')}
      </h2>
      <p class="text-xl text-gray-400 max-w-3xl mx-auto">
        {t('limitations.subtitle')}
      </p>
    </div>

    <!-- Limitations Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
      {limitations.map((limitation) => (
        <div class="bg-gray-800/30 backdrop-blur-sm border border-gray-700/30 rounded-2xl p-8 hover:bg-gray-800/50 transition-all duration-300">
          <!-- Limitation Icon -->
          <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl flex items-center justify-center mb-6">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {limitation.icon === 'fact' && (
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              )}
              {limitation.icon === 'character' && (
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              )}
            </svg>
          </div>

          <!-- Limitation Content -->
          <h3 class="text-xl font-semibold text-white mb-4">
            {limitation.title}
          </h3>
          <p class="text-gray-400 leading-relaxed">
            {limitation.description}
          </p>
        </div>
      ))}
    </div>

    <!-- Additional Note -->
    <div class="mt-12 text-center">
      <p class="text-gray-500 text-sm max-w-2xl mx-auto">
        {t('limitations.note')}
      </p>
    </div>
  </div>
</section>
