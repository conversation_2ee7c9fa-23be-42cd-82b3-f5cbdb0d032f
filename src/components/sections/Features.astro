---
import { getLangFromUrl, useTranslations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<section id="features" class="py-20 bg-gray-900">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section header -->
    <div class="text-center mb-16">
      <h2 class="text-3xl sm:text-4xl font-bold text-white mb-4">
        {t('capabilities.title')}
      </h2>
      <p class="text-xl text-gray-300 max-w-3xl mx-auto">
        {t('capabilities.subtitle')}
      </p>
    </div>

    <!-- Features grid -->
    <div class="grid md:grid-cols-3 gap-8 mb-20">
      <!-- Feature 1: Character Consistency -->
      <div class="group">
        <div class="bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl p-8 h-full hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300 transform hover:-translate-y-2 border border-gray-700/50">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-white mb-4">
            {t('features.consistency.title')}
          </h3>
          <p class="text-gray-300 leading-relaxed">
            {t('features.consistency.description')}
          </p>
        </div>
      </div>

      <!-- Feature 2: Design Boundaries -->
      <div class="group">
        <div class="bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl p-8 h-full hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-300 transform hover:-translate-y-2 border border-gray-700/50">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-white mb-4">
            {t('features.boundaries.title')}
          </h3>
          <p class="text-gray-300 leading-relaxed">
            {t('features.boundaries.description')}
          </p>
        </div>
      </div>

      <!-- Feature 3: Multiple Possibilities -->
      <div class="group">
        <div class="bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl p-8 h-full hover:shadow-2xl hover:shadow-green-500/10 transition-all duration-300 transform hover:-translate-y-2 border border-gray-700/50">
          <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-white mb-4">
            {t('features.possibilities.title')}
          </h3>
          <p class="text-gray-300 leading-relaxed">
            {t('features.possibilities.description')}
          </p>
        </div>
      </div>
    </div>

    <!-- Image showcase grid -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
      <!-- Placeholder for showcase images -->
      <div class="aspect-square bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl flex items-center justify-center group hover:scale-105 transition-all duration-300 border border-gray-600/50 hover:border-blue-500/50">
        <svg class="w-12 h-12 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>

      <div class="aspect-square bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl flex items-center justify-center group hover:scale-105 transition-all duration-300 border border-gray-600/50 hover:border-purple-500/50">
        <svg class="w-12 h-12 text-purple-400 group-hover:text-purple-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>

      <div class="aspect-square bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl flex items-center justify-center group hover:scale-105 transition-all duration-300 border border-gray-600/50 hover:border-green-500/50">
        <svg class="w-12 h-12 text-green-400 group-hover:text-green-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>

      <div class="aspect-square bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl flex items-center justify-center group hover:scale-105 transition-all duration-300 border border-gray-600/50 hover:border-orange-500/50">
        <svg class="w-12 h-12 text-orange-400 group-hover:text-orange-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
    </div>
  </div>
</section>
