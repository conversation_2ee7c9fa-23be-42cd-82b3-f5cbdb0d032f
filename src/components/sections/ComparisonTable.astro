---
import { getLangFromUrl, useTranslations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<section class="py-20 px-4 sm:px-6 lg:px-8">
  <div class="max-w-6xl mx-auto">
    <h2 class="text-3xl font-bold text-white mb-12 text-center">
      {t('comparison.table.title')}
    </h2>
    
    <div class="overflow-x-auto">
      <table class="w-full bg-gray-800/50 border border-gray-700/50 rounded-xl">
        <thead>
          <tr class="border-b border-gray-700/50">
            <th class="text-left p-6 text-white font-semibold">
              {t('comparison.table.headers.capability')}
            </th>
            <th class="text-left p-6 text-blue-400 font-semibold">
              {t('comparison.table.headers.gemini')}
            </th>
            <th class="text-left p-6 text-orange-400 font-semibold">
              {t('comparison.table.headers.imagen')}
            </th>
            <th class="text-left p-6 text-green-400 font-semibold">
              {t('comparison.table.headers.winner')}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr class="border-b border-gray-700/30">
            <td class="p-6 text-gray-300 font-medium">
              {t('comparison.table.rows.characterConsistency.name')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.characterConsistency.geminiRating')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.characterConsistency.imagenRating')}
            </td>
            <td class="p-6 text-blue-400 font-semibold">
              {t('comparison.table.rows.characterConsistency.winner')}
            </td>
          </tr>
          
          <tr class="border-b border-gray-700/30">
            <td class="p-6 text-gray-300 font-medium">
              {t('comparison.table.rows.conversationalEditing.name')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.conversationalEditing.geminiRating')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.conversationalEditing.imagenRating')}
            </td>
            <td class="p-6 text-blue-400 font-semibold">
              {t('comparison.table.rows.conversationalEditing.winner')}
            </td>
          </tr>
          
          <tr class="border-b border-gray-700/30">
            <td class="p-6 text-gray-300 font-medium">
              {t('comparison.table.rows.multiImageFusion.name')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.multiImageFusion.geminiRating')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.multiImageFusion.imagenRating')}
            </td>
            <td class="p-6 text-blue-400 font-semibold">
              {t('comparison.table.rows.multiImageFusion.winner')}
            </td>
          </tr>
          
          <tr class="border-b border-gray-700/30">
            <td class="p-6 text-gray-300 font-medium">
              {t('comparison.table.rows.textRendering.name')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.textRendering.geminiRating')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.textRendering.imagenRating')}
            </td>
            <td class="p-6 text-orange-400 font-semibold">
              {t('comparison.table.rows.textRendering.winner')}
            </td>
          </tr>
          
          <tr class="border-b border-gray-700/30">
            <td class="p-6 text-gray-300 font-medium">
              {t('comparison.table.rows.styleTransfer.name')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.styleTransfer.geminiRating')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.styleTransfer.imagenRating')}
            </td>
            <td class="p-6 text-orange-400 font-semibold">
              {t('comparison.table.rows.styleTransfer.winner')}
            </td>
          </tr>
          
          <tr class="border-b border-gray-700/30">
            <td class="p-6 text-gray-300 font-medium">
              {t('comparison.table.rows.worldKnowledge.name')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.worldKnowledge.geminiRating')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.worldKnowledge.imagenRating')}
            </td>
            <td class="p-6 text-orange-400 font-semibold">
              {t('comparison.table.rows.worldKnowledge.winner')}
            </td>
          </tr>
          
          <tr>
            <td class="p-6 text-gray-300 font-medium">
              {t('comparison.table.rows.censorship.name')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.censorship.geminiRating')}
            </td>
            <td class="p-6 text-gray-300">
              {t('comparison.table.rows.censorship.imagenRating')}
            </td>
            <td class="p-6 text-orange-400 font-semibold">
              {t('comparison.table.rows.censorship.winner')}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>
