---
import { getLangFromUrl, useTranslations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const safetyFeatures = [
  {
    title: t('safety.items.0.title'),
    description: t('safety.items.0.description'),
    icon: "shield"
  },
  {
    title: t('safety.items.1.title'),
    description: t('safety.items.1.description'),
    icon: "responsibility"
  },
  {
    title: t('safety.items.2.title'),
    description: t('safety.items.2.description'),
    icon: "privacy"
  }
];
---

<section class="py-20 bg-gradient-to-b from-gray-900/50 to-gray-800/50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
        {t('safety.title')}
      </h2>
      <p class="text-xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
        {t('safety.subtitle')}
      </p>
    </div>

    <!-- Safety Features Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
      {safetyFeatures.map((feature) => (
        <div class="bg-gray-800/40 backdrop-blur-sm border border-gray-700/40 rounded-2xl p-8 hover:bg-gray-800/60 transition-all duration-300 hover:scale-105">
          <!-- Feature Icon -->
          <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-6">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {feature.icon === 'shield' && (
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              )}
              {feature.icon === 'responsibility' && (
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              )}
              {feature.icon === 'privacy' && (
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              )}
            </svg>
          </div>

          <!-- Feature Content -->
          <h3 class="text-xl font-semibold text-white mb-4">
            {feature.title}
          </h3>
          <p class="text-gray-400 leading-relaxed">
            {feature.description}
          </p>
        </div>
      ))}
    </div>

    <!-- Additional Information -->
    <div class="bg-gray-800/30 backdrop-blur-sm border border-gray-700/30 rounded-2xl p-8">
      <div class="text-center">
        <h3 class="text-2xl font-semibold text-white mb-4">
          {t('safety.commitment.title')}
        </h3>
        <p class="text-gray-400 leading-relaxed max-w-4xl mx-auto mb-6">
          {t('safety.commitment.description')}
        </p>
        <a
          href="https://ai.google/responsibility/principles/"
          target="_blank"
          rel="noopener noreferrer"
          class="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors duration-200"
        >
          {t('safety.commitment.learnMore')}
          <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </a>
      </div>
    </div>
  </div>
</section>
