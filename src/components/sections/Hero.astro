---
import { getLangFromUrl, useTranslations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const heroTitle = t('hero.title');
const heroSubtitle = t('hero.subtitle');
const heroDescription = t('hero.description');
const ctaPrimary = t('hero.cta.primary');
const ctaSecondary = t('hero.cta.secondary');
---

<section class="relative min-h-screen flex items-center justify-center bg-black pt-16 overflow-hidden">
  <!-- Background video -->
  <div class="absolute inset-0">
    <video
      autoplay
      muted
      loop
      playsinline
      class="w-full h-full object-cover opacity-30"
      poster="/images/hero/hero-background.jpg"
    >
      <source src="/videos/hero-background.mp4" type="video/mp4">
      <!-- Fallback background image -->
      <img
        src="/images/hero/hero-background.jpg"
        alt="AI Image Generation Background"
        class="w-full h-full object-cover"
      />
    </video>

    <!-- Video overlay -->
    <div class="absolute inset-0 bg-gradient-to-b from-black/50 via-black/30 to-black/70"></div>
  </div>

  <!-- Background decoration with animations -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-blob"></div>
    <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-blob animation-delay-2000"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl animate-blob animation-delay-4000"></div>

    <!-- Floating particles -->
    <div class="absolute top-20 left-10 w-2 h-2 bg-blue-400/30 rounded-full animate-float"></div>
    <div class="absolute top-40 right-20 w-3 h-3 bg-purple-400/30 rounded-full animate-float animation-delay-1000"></div>
    <div class="absolute bottom-40 left-20 w-2 h-2 bg-pink-400/30 rounded-full animate-float animation-delay-3000"></div>
    <div class="absolute bottom-20 right-10 w-3 h-3 bg-blue-400/30 rounded-full animate-float animation-delay-5000"></div>
    <div class="absolute top-60 left-1/3 w-1 h-1 bg-white/20 rounded-full animate-twinkle"></div>
    <div class="absolute top-80 right-1/3 w-1 h-1 bg-white/20 rounded-full animate-twinkle animation-delay-2000"></div>
    <div class="absolute bottom-60 left-2/3 w-1 h-1 bg-white/20 rounded-full animate-twinkle animation-delay-4000"></div>
  </div>

  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
    <div class="text-center">
      <!-- Main heading -->
      <h1 class="text-5xl sm:text-6xl lg:text-8xl font-bold text-white leading-tight mb-8 animate-fade-in-up">
        <span class="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
          {heroTitle}
        </span>
      </h1>

      <p class="mt-6 text-2xl sm:text-3xl text-gray-200 leading-relaxed max-w-3xl mx-auto font-medium animate-fade-in-up animation-delay-1000">
        {heroSubtitle}
      </p>

      <p class="mt-4 text-lg sm:text-xl text-gray-400 leading-relaxed max-w-4xl mx-auto animate-fade-in-up animation-delay-2000">
        {heroDescription}
      </p>

      <!-- CTA Buttons -->
      <div class="mt-12 flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in-up animation-delay-3000">
        <a
          href="https://gemini.google.com"
          target="_blank"
          rel="noopener noreferrer"
          class="group inline-flex items-center justify-center px-10 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-full hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-2xl hover:shadow-blue-500/25 transform hover:scale-105 text-lg relative overflow-hidden"
        >
          <span class="relative z-10">{ctaPrimary}</span>
          <svg class="ml-3 w-5 h-5 relative z-10 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
          <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </a>

        <a
          href="https://aistudio.google.com"
          target="_blank"
          rel="noopener noreferrer"
          class="group inline-flex items-center justify-center px-10 py-4 border-2 border-gray-600 text-gray-300 font-semibold rounded-full hover:border-gray-400 hover:text-white transition-all duration-300 text-lg hover:bg-gray-800/50 backdrop-blur-sm"
        >
          <span class="relative z-10">{ctaSecondary}</span>
          <svg class="ml-3 w-5 h-5 relative z-10 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </a>
      </div>

      <!-- Stats or features preview -->
      <div class="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
        <div class="text-center p-6 rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 hover:bg-gray-800/70 transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/20">
          <div class="text-4xl mb-4">🤖</div>
          <div class="text-3xl font-bold text-white mb-2">{t('hero.stats.aiPowered')}</div>
          <div class="text-gray-400">{t('hero.stats.aiPoweredDesc')}</div>
        </div>
        <div class="text-center p-6 rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 hover:bg-gray-800/70 transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-purple-500/20">
          <div class="text-4xl mb-4">🎨</div>
          <div class="text-3xl font-bold text-white mb-2">{t('hero.stats.unlimited')}</div>
          <div class="text-gray-400">{t('hero.stats.unlimitedDesc')}</div>
        </div>
        <div class="text-center p-6 rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 hover:bg-gray-800/70 transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20">
          <div class="text-4xl mb-4">⚡</div>
          <div class="text-3xl font-bold text-white mb-2">{t('hero.stats.instant')}</div>
          <div class="text-gray-400">{t('hero.stats.instantDesc')}</div>
        </div>
      </div>

      <!-- Scroll indicator -->
      <div class="mt-16 flex justify-center">
        <div class="animate-bounce">
          <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes twinkle {
    0%, 100% {
      opacity: 0.2;
    }
    50% {
      opacity: 1;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-blob {
    animation: blob 7s infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-twinkle {
    animation: twinkle 4s ease-in-out infinite;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out;
  }

  .animation-delay-1000 {
    animation-delay: 1s;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-3000 {
    animation-delay: 3s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  .animation-delay-5000 {
    animation-delay: 5s;
  }
</style>
