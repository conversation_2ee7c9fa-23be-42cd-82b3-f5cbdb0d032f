---
import { getLangFromUrl, useTranslations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<section class="py-20 px-4 sm:px-6 lg:px-8">
  <div class="max-w-6xl mx-auto">
    <h2 class="text-3xl font-bold text-white mb-12 text-center">
      {t('nanoBanana.timeline.title')}
    </h2>
    
    <div class="relative">
      <!-- Timeline line -->
      <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-yellow-400 via-orange-500 to-red-500"></div>
      
      <div class="space-y-12">
        <!-- Internal Testing Phase -->
        <div class="relative flex items-start space-x-8">
          <div class="flex-shrink-0 w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center z-10">
            <span class="text-2xl">🧪</span>
          </div>
          <div class="flex-1 bg-gray-800/50 border border-gray-700/50 rounded-xl p-8">
            <div class="text-sm text-yellow-400 font-medium mb-2">
              {t('nanoBanana.timeline.phases.internalTesting.period')}
            </div>
            <h3 class="text-xl font-bold text-white mb-4">
              {t('nanoBanana.timeline.phases.internalTesting.title')}
            </h3>
            <p class="text-gray-300 leading-relaxed">
              {t('nanoBanana.timeline.phases.internalTesting.description')}
            </p>
          </div>
        </div>
        
        <!-- Community Discovery Phase -->
        <div class="relative flex items-start space-x-8">
          <div class="flex-shrink-0 w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center z-10">
            <span class="text-2xl">🔍</span>
          </div>
          <div class="flex-1 bg-gray-800/50 border border-gray-700/50 rounded-xl p-8">
            <div class="text-sm text-orange-400 font-medium mb-2">
              {t('nanoBanana.timeline.phases.communityDiscovery.period')}
            </div>
            <h3 class="text-xl font-bold text-white mb-4">
              {t('nanoBanana.timeline.phases.communityDiscovery.title')}
            </h3>
            <p class="text-gray-300 leading-relaxed">
              {t('nanoBanana.timeline.phases.communityDiscovery.description')}
            </p>
          </div>
        </div>
        
        <!-- Official Release Phase -->
        <div class="relative flex items-start space-x-8">
          <div class="flex-shrink-0 w-16 h-16 bg-green-500 rounded-full flex items-center justify-center z-10">
            <span class="text-2xl">🚀</span>
          </div>
          <div class="flex-1 bg-gray-800/50 border border-gray-700/50 rounded-xl p-8">
            <div class="text-sm text-green-400 font-medium mb-2">
              {t('nanoBanana.timeline.phases.officialRelease.period')}
            </div>
            <h3 class="text-xl font-bold text-white mb-4">
              {t('nanoBanana.timeline.phases.officialRelease.title')}
            </h3>
            <p class="text-gray-300 leading-relaxed">
              {t('nanoBanana.timeline.phases.officialRelease.description')}
            </p>
          </div>
        </div>
        
        <!-- Community Concerns Phase -->
        <div class="relative flex items-start space-x-8">
          <div class="flex-shrink-0 w-16 h-16 bg-red-500 rounded-full flex items-center justify-center z-10">
            <span class="text-2xl">⚠️</span>
          </div>
          <div class="flex-1 bg-gray-800/50 border border-gray-700/50 rounded-xl p-8">
            <div class="text-sm text-red-400 font-medium mb-2">
              {t('nanoBanana.timeline.phases.communityConcerns.period')}
            </div>
            <h3 class="text-xl font-bold text-white mb-4">
              {t('nanoBanana.timeline.phases.communityConcerns.title')}
            </h3>
            <p class="text-gray-300 leading-relaxed">
              {t('nanoBanana.timeline.phases.communityConcerns.description')}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
