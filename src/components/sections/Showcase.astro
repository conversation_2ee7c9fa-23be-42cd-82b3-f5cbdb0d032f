---
import Carousel from '../ui/Carousel.astro';
import { getLangFromUrl, useTranslations } from '../../i18n';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Sample showcase images - these will be replaced with actual content
const showcaseImages = [
  {
    src: '/placeholder1.jpg',
    alt: 'AI generated character illustration',
    title: 'Character Consistency',
    description: 'Maintain the same character across multiple scenes and styles'
  },
  {
    src: '/placeholder2.jpg', 
    alt: 'Creative design boundaries',
    title: 'Push Creative Boundaries',
    description: 'Explore new artistic styles and creative possibilities'
  },
  {
    src: '/placeholder3.jpg',
    alt: 'Multiple variations from one prompt',
    title: 'Endless Possibilities',
    description: 'Generate multiple unique variations from a single prompt'
  },
  {
    src: '/placeholder4.jpg',
    alt: 'Professional quality results',
    title: 'Professional Quality',
    description: 'Create high-quality images suitable for any project'
  }
];
---

<section class="py-20 bg-gradient-to-b from-gray-800 to-gray-900">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section header -->
    <div class="text-center mb-16">
      <h2 class="text-3xl sm:text-4xl font-bold text-white mb-4">
        See What's Possible
      </h2>
      <p class="text-xl text-gray-300 max-w-3xl mx-auto">
        Explore examples of what you can create with Flash Image Fun's advanced AI technology
      </p>
    </div>

    <!-- Carousel showcase -->
    <div class="mb-16">
      <Carousel images={showcaseImages} autoplay={true} interval={4000} />
    </div>

    <!-- Feature highlights -->
    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div class="text-center p-6 rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 hover:border-blue-500/50 transition-all duration-300">
        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-white mb-2">Lightning Fast</h3>
        <p class="text-gray-400 text-sm">Generate images in seconds, not minutes</p>
      </div>

      <div class="text-center p-6 rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 hover:border-green-500/50 transition-all duration-300">
        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-white mb-2">High Quality</h3>
        <p class="text-gray-400 text-sm">Professional-grade results every time</p>
      </div>

      <div class="text-center p-6 rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 hover:border-purple-500/50 transition-all duration-300">
        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-white mb-2">Versatile Styles</h3>
        <p class="text-gray-400 text-sm">From photorealistic to artistic styles</p>
      </div>

      <div class="text-center p-6 rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 hover:border-orange-500/50 transition-all duration-300">
        <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-white mb-2">Easy to Use</h3>
        <p class="text-gray-400 text-sm">Simple prompts, amazing results</p>
      </div>
    </div>

    <!-- Call to action -->
    <div class="text-center mt-16">
      <a
        href="#try-now"
        class="inline-flex items-center justify-center px-10 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-full hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-2xl hover:shadow-blue-500/25 transform hover:scale-105 text-lg"
      >
        Start Creating Now
        <svg class="ml-3 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
        </svg>
      </a>
    </div>
  </div>
</section>
