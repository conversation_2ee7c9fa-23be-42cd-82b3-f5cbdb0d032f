---
// Structured Data component for SEO
export interface StructuredDataProps {
  type: 'WebSite' | 'Article' | 'SoftwareApplication' | 'Review' | 'HowTo' | 'FAQPage';
  title?: string;
  description?: string;
  url?: string;
  image?: string;
  author?: string;
  datePublished?: string;
  dateModified?: string;
  rating?: {
    ratingValue: number;
    bestRating: number;
    worstRating: number;
    ratingCount: number;
  };
  applicationCategory?: string;
  operatingSystem?: string;
  offers?: {
    price: string;
    priceCurrency: string;
    availability: string;
  };
}

const { 
  type, 
  title, 
  description, 
  url, 
  image, 
  author, 
  datePublished, 
  dateModified, 
  rating,
  applicationCategory,
  operatingSystem,
  offers
} = Astro.props as StructuredDataProps;

// Generate structured data based on type
let structuredData: any = {
  "@context": "https://schema.org",
  "@type": type
};

// Common properties
if (title) structuredData.name = title;
if (description) structuredData.description = description;
if (url) structuredData.url = url;
if (image) structuredData.image = image;

// Type-specific properties
switch (type) {
  case 'WebSite':
    structuredData = {
      ...structuredData,
      name: title || "Flash Image Fun - Gemini 2.5 Flash Image AI图像生成工具",
      description: description || "专业的Gemini 2.5 Flash Image AI图像生成工具指南，提供开发者教程、模型对比、实战案例和最佳实践。",
      url: url || "https://flashimage.fun",
      potentialAction: {
        "@type": "SearchAction",
        target: {
          "@type": "EntryPoint",
          urlTemplate: "https://flashimage.fun/search?q={search_term_string}"
        },
        "query-input": "required name=search_term_string"
      },
      publisher: {
        "@type": "Organization",
        name: "Flash Image Fun",
        logo: {
          "@type": "ImageObject",
          url: "https://flashimage.fun/logo.png"
        }
      }
    };
    break;

  case 'Article':
    structuredData = {
      ...structuredData,
      headline: title,
      author: {
        "@type": "Person",
        name: author || "Flash Image Fun Team"
      },
      publisher: {
        "@type": "Organization",
        name: "Flash Image Fun",
        logo: {
          "@type": "ImageObject",
          url: "https://flashimage.fun/logo.png"
        }
      },
      datePublished: datePublished,
      dateModified: dateModified || datePublished,
      mainEntityOfPage: {
        "@type": "WebPage",
        "@id": url
      }
    };
    break;

  case 'SoftwareApplication':
    structuredData = {
      ...structuredData,
      applicationCategory: applicationCategory || "AI Image Generation",
      operatingSystem: operatingSystem || "Web Browser",
      offers: offers ? {
        "@type": "Offer",
        price: offers.price,
        priceCurrency: offers.priceCurrency,
        availability: offers.availability
      } : undefined,
      aggregateRating: rating ? {
        "@type": "AggregateRating",
        ratingValue: rating.ratingValue,
        bestRating: rating.bestRating,
        worstRating: rating.worstRating,
        ratingCount: rating.ratingCount
      } : undefined
    };
    break;

  case 'Review':
    structuredData = {
      ...structuredData,
      itemReviewed: {
        "@type": "SoftwareApplication",
        name: "Gemini 2.5 Flash Image",
        applicationCategory: "AI Image Generation"
      },
      author: {
        "@type": "Person",
        name: author || "Flash Image Fun Team"
      },
      reviewRating: rating ? {
        "@type": "Rating",
        ratingValue: rating.ratingValue,
        bestRating: rating.bestRating,
        worstRating: rating.worstRating
      } : undefined,
      publisher: {
        "@type": "Organization",
        name: "Flash Image Fun"
      }
    };
    break;

  case 'HowTo':
    structuredData = {
      ...structuredData,
      totalTime: "PT30M",
      estimatedCost: {
        "@type": "MonetaryAmount",
        currency: "USD",
        value: "0"
      },
      supply: [
        {
          "@type": "HowToSupply",
          name: "Google AI Studio账户"
        },
        {
          "@type": "HowToSupply", 
          name: "API密钥"
        }
      ],
      tool: [
        {
          "@type": "HowToTool",
          name: "Python或JavaScript开发环境"
        }
      ]
    };
    break;

  case 'FAQPage':
    // FAQ structured data will be handled separately for each FAQ item
    break;
}

// Clean up undefined values
Object.keys(structuredData).forEach(key => {
  if (structuredData[key] === undefined) {
    delete structuredData[key];
  }
});
---

<script type="application/ld+json" set:html={JSON.stringify(structuredData)}></script>
