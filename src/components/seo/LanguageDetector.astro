---
// 语言自动检测组件
// 在客户端检测用户的首选语言并重定向
import { getEnabledLanguageCodes } from '../../config/languages';

const supportedLanguages = getEnabledLanguageCodes();
---

<!-- 将服务端语言配置传递给客户端 -->
<script define:vars={{ supportedLanguages }}>
  // 设置全局语言配置
  window.SUPPORTED_LANGUAGES = supportedLanguages;
</script>

<script>
  // 增强的语言自动检测和重定向逻辑
  (function() {
    // 检查是否已经在语言特定的路径上
    const currentPath = window.location.pathname;
    const isOnLanguagePath = /^\/[a-z]{2}(\/|$)/.test(currentPath);

    // 如果已经在语言路径上，不需要重定向
    if (isOnLanguagePath) {
      return;
    }

    // 支持的语言 - 使用动态配置
    const supportedLanguages = window.SUPPORTED_LANGUAGES || ['en'];

    // 扩展的语言映射（处理地区代码和方言）
    const languageMap = {
      // 中文变体
      'zh-CN': 'zh', 'zh-Hans': 'zh', 'zh-Hans-CN': 'zh',
      'zh-TW': 'zh', 'zh-Hant': 'zh', 'zh-Hant-TW': 'zh',
      'zh-HK': 'zh', 'zh-Hant-HK': 'zh',
      'zh-SG': 'zh', 'zh-Hans-SG': 'zh',
      'zh-MO': 'zh', 'zh-Hant-MO': 'zh',

      // 英文变体
      'en-US': 'en', 'en-GB': 'en', 'en-CA': 'en', 'en-AU': 'en',
      'en-NZ': 'en', 'en-ZA': 'en', 'en-IE': 'en', 'en-IN': 'en',

      // 西班牙语变体
      'es-ES': 'es', 'es-MX': 'es', 'es-AR': 'es', 'es-CO': 'es',
      'es-CL': 'es', 'es-PE': 'es', 'es-VE': 'es',

      // 葡萄牙语变体
      'pt-BR': 'pt', 'pt-PT': 'pt',

      // 法语变体
      'fr-FR': 'fr', 'fr-CA': 'fr', 'fr-BE': 'fr', 'fr-CH': 'fr',

      // 德语变体
      'de-DE': 'de', 'de-AT': 'de', 'de-CH': 'de',

      // 意大利语变体
      'it-IT': 'it', 'it-CH': 'it',

      // 俄语变体
      'ru-RU': 'ru', 'ru-BY': 'ru', 'ru-KZ': 'ru',

      // 阿拉伯语变体
      'ar-SA': 'ar', 'ar-EG': 'ar', 'ar-AE': 'ar', 'ar-MA': 'ar',

      // 荷兰语变体
      'nl-NL': 'nl', 'nl-BE': 'nl',

      // 瑞典语变体
      'sv-SE': 'sv',

      // 挪威语变体
      'no-NO': 'no', 'nb-NO': 'no', 'nn-NO': 'no',

      // 丹麦语变体
      'da-DK': 'da',

      // 芬兰语变体
      'fi-FI': 'fi',

      // 其他语言变体
      'ja-JP': 'ja', 'ko-KR': 'ko', 'th-TH': 'th',
      'vi-VN': 'vi', 'hi-IN': 'hi', 'tr-TR': 'tr',
      'pl-PL': 'pl', 'cs-CZ': 'cs', 'uk-UA': 'uk',
      'he-IL': 'he', 'id-ID': 'id', 'ms-MY': 'ms'
    };

    // 地理位置到语言的映射（基于时区推测）
    const timezoneLanguageMap = {
      // 中文区域
      'Asia/Shanghai': 'zh', 'Asia/Beijing': 'zh', 'Asia/Chongqing': 'zh',
      'Asia/Hong_Kong': 'zh', 'Asia/Macau': 'zh', 'Asia/Taipei': 'zh',
      'Asia/Singapore': 'zh',

      // 日语区域
      'Asia/Tokyo': 'ja',

      // 韩语区域
      'Asia/Seoul': 'ko',

      // 泰语区域
      'Asia/Bangkok': 'th',

      // 越南语区域
      'Asia/Ho_Chi_Minh': 'vi', 'Asia/Saigon': 'vi',

      // 印尼语区域
      'Asia/Jakarta': 'id',

      // 马来语区域
      'Asia/Kuala_Lumpur': 'ms',

      // 印地语区域
      'Asia/Kolkata': 'hi', 'Asia/Calcutta': 'hi',

      // 阿拉伯语区域
      'Asia/Riyadh': 'ar', 'Asia/Dubai': 'ar', 'Africa/Cairo': 'ar',

      // 土耳其语区域
      'Europe/Istanbul': 'tr',

      // 俄语区域
      'Europe/Moscow': 'ru', 'Europe/Volgograd': 'ru',

      // 德语区域
      'Europe/Berlin': 'de', 'Europe/Vienna': 'de', 'Europe/Zurich': 'de',

      // 法语区域
      'Europe/Paris': 'fr', 'Europe/Brussels': 'fr',

      // 意大利语区域
      'Europe/Rome': 'it',

      // 西班牙语区域
      'Europe/Madrid': 'es', 'America/Mexico_City': 'es', 'America/Buenos_Aires': 'es',

      // 葡萄牙语区域
      'Europe/Lisbon': 'pt', 'America/Sao_Paulo': 'pt',

      // 荷兰语区域
      'Europe/Amsterdam': 'nl',

      // 瑞典语区域
      'Europe/Stockholm': 'sv',

      // 挪威语区域
      'Europe/Oslo': 'no',

      // 丹麦语区域
      'Europe/Copenhagen': 'da',

      // 芬兰语区域
      'Europe/Helsinki': 'fi',

      // 波兰语区域
      'Europe/Warsaw': 'pl',

      // 捷克语区域
      'Europe/Prague': 'cs',

      // 乌克兰语区域
      'Europe/Kiev': 'uk',

      // 希伯来语区域
      'Asia/Jerusalem': 'he'
    };

    function detectLanguage() {
      // 1. 检查 URL 参数中的语言设置
      const urlParams = new URLSearchParams(window.location.search);
      const urlLang = urlParams.get('lang');
      if (urlLang && supportedLanguages.includes(urlLang)) {
        return urlLang;
      }

      // 2. 检查保存的语言偏好（带过期时间）
      const savedData = localStorage.getItem('language-preference');
      if (savedData) {
        try {
          const { language, timestamp } = JSON.parse(savedData);
          const oneWeek = 7 * 24 * 60 * 60 * 1000; // 一周的毫秒数

          if (Date.now() - timestamp < oneWeek && supportedLanguages.includes(language)) {
            return language;
          }
        } catch (e) {
          // 清除无效的存储数据
          localStorage.removeItem('language-preference');
        }
      }

      // 3. 检测浏览器语言偏好
      const browserLanguages = navigator.languages || [navigator.language];
      for (const browserLang of browserLanguages) {
        // 完整匹配（如 zh-CN）
        if (languageMap[browserLang]) {
          return languageMap[browserLang];
        }

        // 语言代码匹配（如 zh）
        const langCode = browserLang.split('-')[0];
        if (supportedLanguages.includes(langCode)) {
          return langCode;
        }
      }

      // 4. 基于时区的地理位置推测
      try {
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        if (timezoneLanguageMap[timezone]) {
          return timezoneLanguageMap[timezone];
        }
      } catch (e) {
        // 时区检测失败，继续其他方法
      }

      // 5. 基于用户代理字符串的推测
      const userAgent = navigator.userAgent;
      if (/[\u4e00-\u9fff]/.test(userAgent)) {
        // 用户代理包含中文字符，检查是否支持中文
        return supportedLanguages.includes('zh') ? 'zh' : supportedLanguages[0];
      }

      // 6. 默认返回第一个支持的语言
      return supportedLanguages[0] || 'en';
    }

    // 执行语言检测
    const detectedLanguage = detectLanguage();

    // 构建重定向URL
    const newPath = currentPath === '/'
      ? `/${detectedLanguage}`
      : `/${detectedLanguage}${currentPath}`;

    // 保存语言偏好（带时间戳）
    const preferenceData = {
      language: detectedLanguage,
      timestamp: Date.now(),
      detectionMethod: getDetectionMethod(detectedLanguage)
    };
    localStorage.setItem('language-preference', JSON.stringify(preferenceData));

    // 显示检测信息（开发模式）
    if (window.location.hostname === 'localhost') {
      console.log('Language Detection:', {
        detected: detectedLanguage,
        method: preferenceData.detectionMethod,
        browserLanguages: navigator.languages,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      });
    }

    // 执行重定向
    window.location.replace(newPath);

    function getDetectionMethod(lang) {
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('lang') === lang) return 'url-parameter';

      const savedData = localStorage.getItem('language-preference');
      if (savedData) return 'saved-preference';

      const browserLanguages = navigator.languages || [navigator.language];
      for (const browserLang of browserLanguages) {
        if (languageMap[browserLang] === lang || browserLang.split('-')[0] === lang) {
          return 'browser-language';
        }
      }

      try {
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        if (timezoneLanguageMap[timezone] === lang) return 'timezone';
      } catch (e) {}

      if (/[\u4e00-\u9fff]/.test(navigator.userAgent) && lang === 'zh') {
        return 'user-agent';
      }

      return 'default';
    }
  })();
</script>

<style>
  /* 在重定向期间显示加载状态 */
  body {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }
  
  body.loaded {
    opacity: 1;
  }
</style>

<script>
  // 页面加载完成后显示内容
  document.addEventListener('DOMContentLoaded', function() {
    document.body.classList.add('loaded');
  });
</script>
