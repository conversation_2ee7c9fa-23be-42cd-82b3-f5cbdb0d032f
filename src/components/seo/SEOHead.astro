---
import { getLangFromUrl, languages } from "../../i18n";
import { getLanguageConfig } from "../../config/languages";

interface Props {
  title: string;
  description: string;
  image?: string;
  type?: "website" | "article";
  noindex?: boolean;
  canonical?: string;
}

const {
  title,
  description,
  image,
  type = "website",
  noindex = false,
  canonical,
} = Astro.props;

const currentLang = getLangFromUrl(Astro.url);
const languageConfig = getLanguageConfig(currentLang);
const currentPath = Astro.url.pathname;
const siteUrl = "https://flashimage.fun";

// 生成当前页面的完整URL
const fullUrl = `${siteUrl}${currentPath}`;

// 生成默认图片
const defaultImage = `${siteUrl}/og-image.jpg`;
const ogImage = image || defaultImage;

// 生成 hreflang 链接
const hreflangLinks = Object.entries(languages).map(([lang, name]) => {
  // 从当前路径中移除语言前缀，然后添加新的语言前缀
  const pathWithoutLang = currentPath.replace(/^\/[a-z]{2}(\/|$)/, "/");
  const localizedPath = pathWithoutLang === "/" ? "" : pathWithoutLang;
  const href = `${siteUrl}/${lang}${localizedPath}`;

  return { lang, href, name };
});

// 生成规范链接
const canonicalUrl = canonical || fullUrl;

// 生成 robots 内容
const robotsContent = noindex
  ? "noindex, nofollow"
  : "index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1";
---

<!-- 基础 Meta 标签 -->
<title>{title}</title>
<meta name="description" content={description} />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta charset="UTF-8" />

<!-- 语言和地区 -->
<html lang={currentLang}></html>
<meta name="language" content={currentLang} />

<!-- 规范链接 -->
<link rel="canonical" href={canonicalUrl} />

<!-- Hreflang 链接 -->
{
  hreflangLinks.map(({ lang, href }) => (
    <link rel="alternate" hreflang={lang} href={href} />
  ))
}

<!-- 搜索引擎指令 -->
<meta name="robots" content={robotsContent} />

<!-- Open Graph / Facebook -->
<meta property="og:type" content={type} />
<meta property="og:url" content={fullUrl} />
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:image" content={ogImage} />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
<meta property="og:locale" content={languageConfig?.locale || "en_US"} />
<meta property="og:site_name" content="Flash Image Fun" />

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:url" content={fullUrl} />
<meta name="twitter:title" content={title} />
<meta name="twitter:description" content={description} />
<meta name="twitter:image" content={ogImage} />
<meta name="twitter:creator" content="@flashimagefun" />

<!-- 额外的 SEO Meta 标签 -->
<meta name="author" content="Flash Image Fun" />
<meta name="generator" content="Astro" />
<meta name="theme-color" content="#1f2937" />

<!-- 预连接到重要的第三方域名 -->
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link rel="preconnect" href="https://gemini.google.com" />
<link rel="preconnect" href="https://aistudio.google.com" />

<!-- DNS 预取 -->
<link rel="dns-prefetch" href="//google.com" />
<link rel="dns-prefetch" href="//googleapis.com" />

<!-- Favicon -->
<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
<link rel="icon" type="image/png" href="/favicon.png" />
<link rel="apple-touch-icon" href="/apple-touch-icon.png" />

<!-- 结构化数据会在页面中单独添加 -->
