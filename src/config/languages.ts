/**
 * 统一的多语言配置系统
 * 支持26种语言，遵循Google SEO规范
 */

export interface LanguageConfig {
  code: string;           // ISO 639-1 语言代码
  name: string;           // 本地化语言名称
  englishName: string;    // 英文名称
  flag: string;           // 国旗emoji
  region: string;         // 地区
  rtl: boolean;          // 是否从右到左
  enabled: boolean;       // 是否启用
  translationProgress: number; // 翻译完成度 (0-100)
  hreflang: string;      // hreflang 属性值
  locale: string;        // 完整的locale代码
}

// 26种语言的完整配置
export const LANGUAGE_CONFIGS: Record<string, LanguageConfig> = {
  // 欧洲语言 (14种)
  en: {
    code: 'en',
    name: 'English',
    englishName: 'English',
    flag: '🇺🇸',
    region: 'Europe/Americas',
    rtl: false,
    enabled: true,
    translationProgress: 100,
    hreflang: 'en',
    locale: 'en-US'
  },
  de: {
    code: 'de',
    name: 'Deuts<PERSON>',
    englishName: 'German',
    flag: '🇩🇪',
    region: 'Europe',
    rtl: false,
    enabled: true,
    translationProgress: 10,
    hreflang: 'de',
    locale: 'de-DE'
  },
  fr: {
    code: 'fr',
    name: 'Français',
    englishName: 'French',
    flag: '🇫🇷',
    region: 'Europe',
    rtl: false,
    enabled: true,
    translationProgress: 10,
    hreflang: 'fr',
    locale: 'fr-FR'
  },
  it: {
    code: 'it',
    name: 'Italiano',
    englishName: 'Italian',
    flag: '🇮🇹',
    region: 'Europe',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'it',
    locale: 'it-IT'
  },
  es: {
    code: 'es',
    name: 'Español',
    englishName: 'Spanish',
    flag: '🇪🇸',
    region: 'Europe/Americas',
    rtl: false,
    enabled: true,
    translationProgress: 15,
    hreflang: 'es',
    locale: 'es-ES'
  },
  pt: {
    code: 'pt',
    name: 'Português',
    englishName: 'Portuguese',
    flag: '🇵🇹',
    region: 'Europe/Americas',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'pt',
    locale: 'pt-PT'
  },
  nl: {
    code: 'nl',
    name: 'Nederlands',
    englishName: 'Dutch',
    flag: '🇳🇱',
    region: 'Europe',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'nl',
    locale: 'nl-NL'
  },
  sv: {
    code: 'sv',
    name: 'Svenska',
    englishName: 'Swedish',
    flag: '🇸🇪',
    region: 'Europe',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'sv',
    locale: 'sv-SE'
  },
  da: {
    code: 'da',
    name: 'Dansk',
    englishName: 'Danish',
    flag: '🇩🇰',
    region: 'Europe',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'da',
    locale: 'da-DK'
  },
  no: {
    code: 'no',
    name: 'Norsk',
    englishName: 'Norwegian',
    flag: '🇳🇴',
    region: 'Europe',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'no',
    locale: 'no-NO'
  },
  fi: {
    code: 'fi',
    name: 'Suomi',
    englishName: 'Finnish',
    flag: '🇫🇮',
    region: 'Europe',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'fi',
    locale: 'fi-FI'
  },
  pl: {
    code: 'pl',
    name: 'Polski',
    englishName: 'Polish',
    flag: '🇵🇱',
    region: 'Europe',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'pl',
    locale: 'pl-PL'
  },
  cs: {
    code: 'cs',
    name: 'Čeština',
    englishName: 'Czech',
    flag: '🇨🇿',
    region: 'Europe',
    rtl: false,
    enabled: true,
    translationProgress: 100,
    hreflang: 'cs',
    locale: 'cs-CZ'
  },
  uk: {
    code: 'uk',
    name: 'Українська',
    englishName: 'Ukrainian',
    flag: '🇺🇦',
    region: 'Europe',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'uk',
    locale: 'uk-UA'
  },

  // 亚洲语言 (8种)
  zh: {
    code: 'zh',
    name: '中文',
    englishName: 'Chinese',
    flag: '🇨🇳',
    region: 'Asia',
    rtl: false,
    enabled: true,
    translationProgress: 10,
    hreflang: 'zh',
    locale: 'zh-CN'
  },
  ja: {
    code: 'ja',
    name: '日本語',
    englishName: 'Japanese',
    flag: '🇯🇵',
    region: 'Asia',
    rtl: false,
    enabled: true,
    translationProgress: 10,
    hreflang: 'ja',
    locale: 'ja-JP'
  },
  ko: {
    code: 'ko',
    name: '한국어',
    englishName: 'Korean',
    flag: '🇰🇷',
    region: 'Asia',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'ko',
    locale: 'ko-KR'
  },
  th: {
    code: 'th',
    name: 'ไทย',
    englishName: 'Thai',
    flag: '🇹🇭',
    region: 'Asia',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'th',
    locale: 'th-TH'
  },
  vi: {
    code: 'vi',
    name: 'Tiếng Việt',
    englishName: 'Vietnamese',
    flag: '🇻🇳',
    region: 'Asia',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'vi',
    locale: 'vi-VN'
  },
  id: {
    code: 'id',
    name: 'Bahasa Indonesia',
    englishName: 'Indonesian',
    flag: '🇮🇩',
    region: 'Asia',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'id',
    locale: 'id-ID'
  },
  ms: {
    code: 'ms',
    name: 'Bahasa Melayu',
    englishName: 'Malay',
    flag: '🇲🇾',
    region: 'Asia',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'ms',
    locale: 'ms-MY'
  },
  hi: {
    code: 'hi',
    name: 'हिन्दी',
    englishName: 'Hindi',
    flag: '🇮🇳',
    region: 'Asia',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'hi',
    locale: 'hi-IN'
  },

  // 中东语言 (3种)
  ar: {
    code: 'ar',
    name: 'العربية',
    englishName: 'Arabic',
    flag: '🇸🇦',
    region: 'Middle East',
    rtl: true,
    enabled: true,
    translationProgress: 5,
    hreflang: 'ar',
    locale: 'ar-SA'
  },
  tr: {
    code: 'tr',
    name: 'Türkçe',
    englishName: 'Turkish',
    flag: '🇹🇷',
    region: 'Middle East',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'tr',
    locale: 'tr-TR'
  },
  he: {
    code: 'he',
    name: 'עברית',
    englishName: 'Hebrew',
    flag: '🇮🇱',
    region: 'Middle East',
    rtl: true,
    enabled: true,
    translationProgress: 5,
    hreflang: 'he',
    locale: 'he-IL'
  },

  // 东欧语言 (1种)
  ru: {
    code: 'ru',
    name: 'Русский',
    englishName: 'Russian',
    flag: '🇷🇺',
    region: 'Eastern Europe',
    rtl: false,
    enabled: true,
    translationProgress: 5,
    hreflang: 'ru',
    locale: 'ru-RU'
  }
};

// 工具函数
export const getEnabledLanguages = () => {
  return Object.values(LANGUAGE_CONFIGS).filter(lang => lang.enabled);
};

export const getLanguageConfig = (code: string): LanguageConfig | undefined => {
  return LANGUAGE_CONFIGS[code];
};

export const getAllLanguageCodes = () => {
  return Object.keys(LANGUAGE_CONFIGS);
};

export const getEnabledLanguageCodes = () => {
  return getEnabledLanguages().map(lang => lang.code);
};

// 按地区分组
export const getLanguagesByRegion = () => {
  const regions: Record<string, LanguageConfig[]> = {};
  Object.values(LANGUAGE_CONFIGS).forEach(lang => {
    if (!regions[lang.region]) {
      regions[lang.region] = [];
    }
    regions[lang.region].push(lang);
  });
  return regions;
};

// RTL语言检查
export const isRTLLanguage = (code: string): boolean => {
  const config = getLanguageConfig(code);
  return config?.rtl || false;
};

// 默认语言
export const DEFAULT_LANGUAGE = 'en';
