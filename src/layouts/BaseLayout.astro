---
import '../styles/global.css';
import SEOHead from '../components/seo/SEOHead.astro';
import MobileOptimizations from '../components/ui/MobileOptimizations.astro';
import PageTransitions from '../components/ui/PageTransitions.astro';
import { getLangFromUrl, isValidLang, loadTranslation } from '../i18n';
import { isRTLLanguage } from '../config/languages';

export interface Props {
  title?: string;
  description?: string;
  image?: string;
  type?: 'website' | 'article';
  noindex?: boolean;
  canonical?: string;
  lang?: string;
}

const { title, description, image, type, noindex, canonical, lang } = Astro.props;

// 获取当前语言
const currentLang = lang || getLangFromUrl(Astro.url);
const isRTL = isRTLLanguage(currentLang);

// 确保在渲染子组件前加载当前语言的翻译
await loadTranslation(currentLang);

const pageTitle = title || 'Flash Image Fun';
const pageDescription = description || 'Create and edit images with AI - Bring your imagination to life with advanced image generation and editing capabilities';
---

<!DOCTYPE html>
<html lang={currentLang} dir={isRTL ? 'rtl' : 'ltr'}>
  <head>
    <SEOHead
      title={pageTitle}
      description={pageDescription}
      image={image}
      type={type}
      noindex={noindex}
      canonical={canonical}
    />
    <MobileOptimizations />
    <PageTransitions />
  </head>
  <body class="bg-gray-900 text-white font-sans antialiased">
    <slot />
  </body>
</html>
