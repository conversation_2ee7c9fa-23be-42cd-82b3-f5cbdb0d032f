/**
 * 翻译文件验证工具
 * 用于验证翻译文件的完整性和正确性
 */

import { readFileSync } from 'fs';
import { join } from 'path';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  missingKeys: string[];
  extraKeys: string[];
}

/**
 * 获取JSON对象的所有键路径
 */
function getKeyPaths(obj: any, prefix = ''): string[] {
  const keys: string[] = [];
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const currentPath = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        keys.push(...getKeyPaths(obj[key], currentPath));
      } else {
        keys.push(currentPath);
      }
    }
  }
  
  return keys;
}

/**
 * 验证翻译文件的结构和内容
 */
export function validateTranslationFile(
  filePath: string,
  referenceFilePath: string
): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    missingKeys: [],
    extraKeys: []
  };

  try {
    // 读取参考文件（通常是 en.json）
    const referenceContent = readFileSync(referenceFilePath, 'utf-8');
    const referenceData = JSON.parse(referenceContent);
    const referenceKeys = getKeyPaths(referenceData);

    // 读取待验证文件
    const targetContent = readFileSync(filePath, 'utf-8');
    const targetData = JSON.parse(targetContent);
    const targetKeys = getKeyPaths(targetData);

    // 检查缺失的键
    result.missingKeys = referenceKeys.filter(key => !targetKeys.includes(key));
    
    // 检查多余的键
    result.extraKeys = targetKeys.filter(key => !referenceKeys.includes(key));

    // 验证结果
    if (result.missingKeys.length > 0) {
      result.isValid = false;
      result.errors.push(`Missing keys: ${result.missingKeys.join(', ')}`);
    }

    if (result.extraKeys.length > 0) {
      result.warnings.push(`Extra keys found: ${result.extraKeys.join(', ')}`);
    }

    // 检查空值
    const emptyKeys = targetKeys.filter(key => {
      const value = key.split('.').reduce((obj, k) => obj && obj[k], targetData);
      return !value || (typeof value === 'string' && value.trim() === '');
    });

    if (emptyKeys.length > 0) {
      result.warnings.push(`Empty values found: ${emptyKeys.join(', ')}`);
    }

  } catch (error) {
    result.isValid = false;
    if (error instanceof SyntaxError) {
      result.errors.push(`JSON syntax error: ${error.message}`);
    } else {
      result.errors.push(`File reading error: ${error}`);
    }
  }

  return result;
}

/**
 * 验证所有翻译文件
 */
export function validateAllTranslations(i18nDir: string, referenceFile = 'en.json'): Record<string, ValidationResult> {
  const results: Record<string, ValidationResult> = {};
  const referenceFilePath = join(i18nDir, referenceFile);

  try {
    const fs = require('fs');
    const files = fs.readdirSync(i18nDir);
    
    for (const file of files) {
      if (file.endsWith('.json') && file !== referenceFile) {
        const filePath = join(i18nDir, file);
        const langCode = file.replace('.json', '');
        results[langCode] = validateTranslationFile(filePath, referenceFilePath);
      }
    }
  } catch (error) {
    console.error('Error validating translations:', error);
  }

  return results;
}

/**
 * 生成翻译完整性报告
 */
export function generateTranslationReport(validationResults: Record<string, ValidationResult>): string {
  let report = '# Translation Validation Report\n\n';
  
  for (const [lang, result] of Object.entries(validationResults)) {
    report += `## ${lang.toUpperCase()}\n`;
    report += `Status: ${result.isValid ? '✅ Valid' : '❌ Invalid'}\n\n`;
    
    if (result.errors.length > 0) {
      report += '### Errors:\n';
      result.errors.forEach(error => report += `- ${error}\n`);
      report += '\n';
    }
    
    if (result.warnings.length > 0) {
      report += '### Warnings:\n';
      result.warnings.forEach(warning => report += `- ${warning}\n`);
      report += '\n';
    }
    
    if (result.missingKeys.length > 0) {
      report += '### Missing Keys:\n';
      result.missingKeys.forEach(key => report += `- ${key}\n`);
      report += '\n';
    }
    
    report += '---\n\n';
  }
  
  return report;
}