/**
 * 翻译管理工具
 * 用于管理翻译文件的创建、更新和进度跟踪
 */

import { writeFileSync, readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { LANGUAGE_CONFIGS, getEnabledLanguageCodes } from '../config/languages';

export interface TranslationProgress {
  langCode: string;
  progress: number;
  lastUpdated: string;
  fileExists: boolean;
  keyCount: number;
}

/**
 * 创建翻译文件模板
 */
export function createTranslationTemplate(
  sourcePath: string,
  targetPath: string,
  langCode: string
): boolean {
  try {
    if (existsSync(targetPath)) {
      console.warn(`Translation file already exists: ${targetPath}`);
      return false;
    }

    const sourceContent = readFileSync(sourcePath, 'utf-8');
    const sourceData = JSON.parse(sourceContent);
    
    // 创建模板，保持结构但清空需要翻译的值
    const template = createEmptyTemplate(sourceData, langCode);
    
    writeFileSync(targetPath, JSON.stringify(template, null, 2), 'utf-8');
    console.log(`Created translation template: ${targetPath}`);
    return true;
  } catch (error) {
    console.error(`Error creating translation template: ${error}`);
    return false;
  }
}

/**
 * 创建空的翻译模板
 */
function createEmptyTemplate(obj: any, langCode: string): any {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => createEmptyTemplate(item, langCode));
  }

  const result: any = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      
      if (typeof value === 'string') {
        // 保留某些不需要翻译的内容
        if (shouldPreserveValue(key, value)) {
          result[key] = value;
        } else {
          result[key] = `[${langCode.toUpperCase()}] ${value}`;
        }
      } else {
        result[key] = createEmptyTemplate(value, langCode);
      }
    }
  }
  
  return result;
}

/**
 * 判断是否应该保留原值（不需要翻译）
 */
function shouldPreserveValue(key: string, value: string): boolean {
  // URL 链接
  if (value.startsWith('http://') || value.startsWith('https://')) {
    return true;
  }
  
  // 邮箱地址
  if (value.includes('@') && value.includes('.')) {
    return true;
  }
  
  // 图片路径
  if (value.startsWith('/images/') || value.startsWith('./images/')) {
    return true;
  }
  
  // 特定的键名
  const preserveKeys = ['src', 'alt', 'url', 'contact'];
  if (preserveKeys.includes(key.toLowerCase())) {
    return true;
  }
  
  return false;
}

/**
 * 计算翻译进度
 */
export function calculateTranslationProgress(
  translationPath: string,
  referencePath: string
): number {
  try {
    if (!existsSync(translationPath)) {
      return 0;
    }

    const translationContent = readFileSync(translationPath, 'utf-8');
    const referenceContent = readFileSync(referencePath, 'utf-8');
    
    const translationData = JSON.parse(translationContent);
    const referenceData = JSON.parse(referenceContent);
    
    const referenceKeys = getAllTranslatableKeys(referenceData);
    const translatedKeys = getTranslatedKeys(translationData, referenceData);
    
    return Math.round((translatedKeys.length / referenceKeys.length) * 100);
  } catch (error) {
    console.error(`Error calculating translation progress: ${error}`);
    return 0;
  }
}

/**
 * 获取所有需要翻译的键
 */
function getAllTranslatableKeys(obj: any, prefix = ''): string[] {
  const keys: string[] = [];
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const currentPath = prefix ? `${prefix}.${key}` : key;
      const value = obj[key];
      
      if (typeof value === 'string' && !shouldPreserveValue(key, value)) {
        keys.push(currentPath);
      } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        keys.push(...getAllTranslatableKeys(value, currentPath));
      } else if (Array.isArray(value)) {
        value.forEach((item, index) => {
          if (typeof item === 'object' && item !== null) {
            keys.push(...getAllTranslatableKeys(item, `${currentPath}.${index}`));
          }
        });
      }
    }
  }
  
  return keys;
}

/**
 * 获取已翻译的键
 */
function getTranslatedKeys(translationData: any, referenceData: any): string[] {
  const translatableKeys = getAllTranslatableKeys(referenceData);
  const translatedKeys: string[] = [];
  
  for (const key of translatableKeys) {
    const translatedValue = getValueByPath(translationData, key);
    const referenceValue = getValueByPath(referenceData, key);
    
    if (translatedValue && 
        typeof translatedValue === 'string' && 
        translatedValue.trim() !== '' &&
        translatedValue !== referenceValue &&
        !translatedValue.startsWith('[')) {
      translatedKeys.push(key);
    }
  }
  
  return translatedKeys;
}

/**
 * 根据路径获取值
 */
function getValueByPath(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    if (current && typeof current === 'object') {
      return current[key];
    }
    return undefined;
  }, obj);
}

/**
 * 更新语言配置中的翻译进度
 */
export function updateLanguageProgress(langCode: string, progress: number): boolean {
  try {
    const configPath = join(process.cwd(), 'src/config/languages.ts');
    let configContent = readFileSync(configPath, 'utf-8');
    
    // 使用正则表达式更新进度
    const regex = new RegExp(
      `(${langCode}:\\s*{[^}]*translationProgress:\\s*)\\d+`,
      'g'
    );
    
    configContent = configContent.replace(regex, `$1${progress}`);
    
    writeFileSync(configPath, configContent, 'utf-8');
    console.log(`Updated translation progress for ${langCode}: ${progress}%`);
    return true;
  } catch (error) {
    console.error(`Error updating language progress: ${error}`);
    return false;
  }
}

/**
 * 获取所有语言的翻译进度
 */
export function getAllTranslationProgress(i18nDir: string): TranslationProgress[] {
  const progress: TranslationProgress[] = [];
  const enabledLanguages = getEnabledLanguageCodes();
  const referencePath = join(i18nDir, 'en.json');
  
  for (const langCode of enabledLanguages) {
    const translationPath = join(i18nDir, `${langCode}.json`);
    const fileExists = existsSync(translationPath);
    const progressPercent = fileExists ? 
      calculateTranslationProgress(translationPath, referencePath) : 0;
    
    let keyCount = 0;
    if (fileExists) {
      try {
        const content = readFileSync(translationPath, 'utf-8');
        const data = JSON.parse(content);
        keyCount = getAllTranslatableKeys(data).length;
      } catch (error) {
        console.warn(`Error reading ${langCode}.json: ${error}`);
      }
    }
    
    progress.push({
      langCode,
      progress: progressPercent,
      lastUpdated: fileExists ? new Date().toISOString() : '',
      fileExists,
      keyCount
    });
  }
  
  return progress;
}

/**
 * 生成翻译进度报告
 */
export function generateProgressReport(progressData: TranslationProgress[]): string {
  let report = '# Translation Progress Report\n\n';
  
  const totalLanguages = progressData.length;
  const completedLanguages = progressData.filter(p => p.progress === 100).length;
  const inProgressLanguages = progressData.filter(p => p.progress > 0 && p.progress < 100).length;
  const notStartedLanguages = progressData.filter(p => p.progress === 0).length;
  
  report += `## Summary\n`;
  report += `- Total Languages: ${totalLanguages}\n`;
  report += `- Completed: ${completedLanguages}\n`;
  report += `- In Progress: ${inProgressLanguages}\n`;
  report += `- Not Started: ${notStartedLanguages}\n\n`;
  
  report += `## Progress by Language\n\n`;
  report += `| Language | Progress | Status | Keys | File Exists |\n`;
  report += `|----------|----------|--------|------|-------------|\n`;
  
  // 按进度排序
  const sortedProgress = [...progressData].sort((a, b) => b.progress - a.progress);
  
  for (const lang of sortedProgress) {
    const status = lang.progress === 100 ? '✅ Complete' : 
                  lang.progress > 0 ? '🔄 In Progress' : '⏳ Not Started';
    const fileStatus = lang.fileExists ? '✅' : '❌';
    
    report += `| ${lang.langCode.toUpperCase()} | ${lang.progress}% | ${status} | ${lang.keyCount} | ${fileStatus} |\n`;
  }
  
  return report;
}