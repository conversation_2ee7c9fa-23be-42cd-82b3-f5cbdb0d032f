/**
 * 翻译质量检查工具
 * 用于检查翻译内容的质量和一致性
 */

import { getEnabledLanguageCodes } from '../config/languages';

export interface TranslationQualityReport {
  language: string;
  totalKeys: number;
  translatedKeys: number;
  emptyKeys: number;
  completionPercentage: number;
  issues: TranslationIssue[];
}

export interface TranslationIssue {
  type: 'empty' | 'untranslated' | 'suspicious' | 'formatting';
  key: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

/**
 * 检查翻译内容质量
 */
export function checkTranslationQuality(
  translations: Record<string, any>,
  langCode: string,
  referenceTranslations: Record<string, any>
): TranslationQualityReport {
  const report: TranslationQualityReport = {
    language: langCode,
    totalKeys: 0,
    translatedKeys: 0,
    emptyKeys: 0,
    completionPercentage: 0,
    issues: []
  };

  const langTranslations = translations[langCode];
  const referenceKeys = getAllKeys(referenceTranslations);
  
  report.totalKeys = referenceKeys.length;

  for (const key of referenceKeys) {
    const value = getValueByPath(langTranslations, key);
    const referenceValue = getValueByPath(referenceTranslations, key);

    if (!value || (typeof value === 'string' && value.trim() === '')) {
      report.emptyKeys++;
      report.issues.push({
        type: 'empty',
        key,
        message: 'Translation is empty or missing',
        severity: 'error'
      });
    } else if (value === referenceValue) {
      // 检查是否只是复制了英文内容
      if (langCode !== 'en') {
        report.issues.push({
          type: 'untranslated',
          key,
          message: 'Content appears to be untranslated (same as reference)',
          severity: 'warning'
        });
      } else {
        report.translatedKeys++;
      }
    } else {
      report.translatedKeys++;
      
      // 检查可疑的翻译
      if (typeof value === 'string' && typeof referenceValue === 'string') {
        // 检查是否包含不应该翻译的品牌名称
        const brandNames = ['Flash Image Fun', 'Gemini 2.5 Flash Image', 'Google AI Studio'];
        const suspiciousBrands = brandNames.filter(brand => 
          referenceValue.includes(brand) && !value.includes(brand)
        );
        
        if (suspiciousBrands.length > 0) {
          report.issues.push({
            type: 'suspicious',
            key,
            message: `Brand names may be incorrectly translated: ${suspiciousBrands.join(', ')}`,
            severity: 'warning'
          });
        }

        // 检查URL是否被错误翻译
        const urlPattern = /https?:\/\/[^\s]+/g;
        const referenceUrls = referenceValue.match(urlPattern) || [];
        const translatedUrls = value.match(urlPattern) || [];
        
        if (referenceUrls.length !== translatedUrls.length) {
          report.issues.push({
            type: 'formatting',
            key,
            message: 'URL count mismatch between reference and translation',
            severity: 'error'
          });
        }
      }
    }
  }

  report.completionPercentage = Math.round((report.translatedKeys / report.totalKeys) * 100);

  return report;
}

/**
 * 获取对象的所有键路径
 */
function getAllKeys(obj: any, prefix = ''): string[] {
  const keys: string[] = [];
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const currentPath = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        keys.push(...getAllKeys(obj[key], currentPath));
      } else {
        keys.push(currentPath);
      }
    }
  }
  
  return keys;
}

/**
 * 根据路径获取对象的值
 */
function getValueByPath(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current && current[key], obj);
}

/**
 * 生成翻译质量报告
 */
export function generateQualityReport(reports: TranslationQualityReport[]): string {
  let report = '# Translation Quality Report\n\n';
  
  // 总体统计
  const totalLanguages = reports.length;
  const averageCompletion = reports.reduce((sum, r) => sum + r.completionPercentage, 0) / totalLanguages;
  
  report += `## Summary\n`;
  report += `- Total Languages: ${totalLanguages}\n`;
  report += `- Average Completion: ${averageCompletion.toFixed(1)}%\n\n`;
  
  // 按完成度排序
  const sortedReports = [...reports].sort((a, b) => b.completionPercentage - a.completionPercentage);
  
  report += `## Language Status\n\n`;
  report += `| Language | Completion | Translated | Total | Issues |\n`;
  report += `|----------|------------|------------|-------|--------|\n`;
  
  for (const langReport of sortedReports) {
    const errorCount = langReport.issues.filter(i => i.severity === 'error').length;
    const warningCount = langReport.issues.filter(i => i.severity === 'warning').length;
    const issuesSummary = `${errorCount}E, ${warningCount}W`;
    
    report += `| ${langReport.language.toUpperCase()} | ${langReport.completionPercentage}% | ${langReport.translatedKeys} | ${langReport.totalKeys} | ${issuesSummary} |\n`;
  }
  
  report += '\n';
  
  // 详细问题报告
  for (const langReport of sortedReports) {
    if (langReport.issues.length > 0) {
      report += `## ${langReport.language.toUpperCase()} Issues\n\n`;
      
      const errorIssues = langReport.issues.filter(i => i.severity === 'error');
      const warningIssues = langReport.issues.filter(i => i.severity === 'warning');
      
      if (errorIssues.length > 0) {
        report += `### Errors (${errorIssues.length})\n`;
        errorIssues.forEach(issue => {
          report += `- **${issue.key}**: ${issue.message}\n`;
        });
        report += '\n';
      }
      
      if (warningIssues.length > 0) {
        report += `### Warnings (${warningIssues.length})\n`;
        warningIssues.forEach(issue => {
          report += `- **${issue.key}**: ${issue.message}\n`;
        });
        report += '\n';
      }
    }
  }
  
  return report;
}

/**
 * 检查所有语言的翻译质量
 */
export function checkAllTranslationQuality(
  translations: Record<string, any>,
  referenceTranslations: Record<string, any>
): TranslationQualityReport[] {
  const reports: TranslationQualityReport[] = [];
  const enabledLanguages = getEnabledLanguageCodes();
  
  for (const langCode of enabledLanguages) {
    if (translations[langCode]) {
      const report = checkTranslationQuality(translations, langCode, referenceTranslations);
      reports.push(report);
    }
  }
  
  return reports;
}