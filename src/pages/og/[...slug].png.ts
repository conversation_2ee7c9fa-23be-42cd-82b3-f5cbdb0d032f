import type { APIRoute } from 'astro';

// 简单的 OG 图片生成器
// 在生产环境中，您可能想要使用 @vercel/og 或类似的库

export const GET: APIRoute = async ({ params }) => {
  const { slug } = params;
  
  // 这里是一个简单的 SVG 生成示例
  // 在实际项目中，您可能想要使用更复杂的图片生成库
  
  const title = getPageTitle(slug as string);
  const description = getPageDescription(slug as string);
  
  const svg = `
    <svg width="1200" height="630" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#111827;stop-opacity:1" />
        </linearGradient>
      </defs>
      
      <!-- 背景 -->
      <rect width="1200" height="630" fill="url(#bg)"/>
      
      <!-- 标题 -->
      <text x="60" y="200" font-family="system-ui, -apple-system, sans-serif" 
            font-size="64" font-weight="bold" fill="white">
        ${escapeXml(title)}
      </text>
      
      <!-- 描述 -->
      <text x="60" y="280" font-family="system-ui, -apple-system, sans-serif" 
            font-size="32" fill="#9ca3af">
        ${escapeXml(description)}
      </text>
      
      <!-- Logo/品牌 -->
      <text x="60" y="550" font-family="system-ui, -apple-system, sans-serif" 
            font-size="28" fill="#3b82f6">
        Flash Image Fun
      </text>
      
      <!-- 装饰元素 -->
      <circle cx="1000" cy="150" r="100" fill="#3b82f6" opacity="0.1"/>
      <circle cx="1100" cy="300" r="80" fill="#8b5cf6" opacity="0.1"/>
      <circle cx="950" cy="400" r="60" fill="#ec4899" opacity="0.1"/>
    </svg>
  `;
  
  // 在实际项目中，您可能想要将 SVG 转换为 PNG
  // 这里我们直接返回 SVG 作为示例
  return new Response(svg, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'public, max-age=31536000, immutable'
    }
  });
};

function getPageTitle(slug: string): string {
  // 简化的标题映射，避免在构建时使用动态导入
  const titles: Record<string, string> = {
    'en': 'Flash Image Fun - AI Image Generation',
    'zh': 'Flash Image Fun - AI图像生成',
    'en/developer-guide': 'Developer Guide - Gemini 2.5 Flash Image',
    'zh/developer-guide': '开发者指南 - Gemini 2.5 Flash Image',
    'en/gemini-vs-imagen': 'Gemini vs Imagen - AI Model Comparison',
    'zh/gemini-vs-imagen': 'Gemini vs Imagen - AI模型对比',
    'en/nano-banana-truth': 'The nano-banana Truth - Gemini 2.5 Flash Image',
    'zh/nano-banana-truth': 'nano-banana的真相 - Gemini 2.5 Flash Image'
  };
  
  return titles[slug] || 'Flash Image Fun';
}



function getPageDescription(slug: string): string {
  // 简化的描述映射，避免在构建时使用动态导入
  const descriptions: Record<string, string> = {
    'en': 'Create amazing images with AI',
    'zh': '用AI创建令人惊叹的图像',
    'en/developer-guide': 'Complete API integration guide',
    'zh/developer-guide': '完整的API集成指南',
    'en/gemini-vs-imagen': 'Detailed model comparison',
    'zh/gemini-vs-imagen': '详细的模型对比分析',
    'en/nano-banana-truth': 'The story behind the codename',
    'zh/nano-banana-truth': '代号背后的故事'
  };
  
  return descriptions[slug] || 'AI Image Generation Tool';
}

function escapeXml(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

// 生成静态路径
export async function getStaticPaths() {
  const { getEnabledLanguageCodes } = await import('../../config/languages');
  const languages = getEnabledLanguageCodes();
  const pages = ['', 'developer-guide', 'gemini-vs-imagen', 'nano-banana-truth'];
  
  const paths = [];
  
  for (const lang of languages) {
    for (const page of pages) {
      const slug = page === '' ? lang : `${lang}/${page}`;
      paths.push({ params: { slug } });
    }
  }
  
  return paths;
}
