---
import BaseLayout from '../../layouts/BaseLayout.astro';
import Header from '../../components/layout/Header.astro';
import Footer from '../../components/layout/Footer.astro';
import Hero from '../../components/sections/Hero.astro';
import Stats from '../../components/sections/Stats.astro';
import ImageCarousel from '../../components/sections/ImageCarousel.astro';
import KeyFeatures from '../../components/sections/KeyFeatures.astro';
import Testimonials from '../../components/sections/Testimonials.astro';
import Limitations from '../../components/sections/Limitations.astro';
import SafetyResponsibility from '../../components/sections/SafetyResponsibility.astro';
import TryGemini from '../../components/sections/TryGemini.astro';
import StructuredData from '../../components/seo/StructuredData.astro';
import { getLangFromUrl, useTranslations, isValidLang } from '../../i18n';

// 获取语言参数
const { lang } = Astro.params;

// 验证语言参数
if (!lang || !isValidLang(lang)) {
  return Astro.redirect('/en');
}

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

const pageTitle = t('pages.index.title');
const pageDescription = t('pages.index.description');

// 生成静态路径 - 动态支持所有启用的语言
export async function getStaticPaths() {
  // 动态导入语言配置
  const { getEnabledLanguageCodes } = await import('../../config/languages');
  const enabledLanguages = getEnabledLanguageCodes();

  return enabledLanguages.map(lang => ({
    params: { lang }
  }));
}
---

<BaseLayout title={pageTitle} description={pageDescription}>
  <StructuredData 
    data={{
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": t('site.title'),
      "description": t('site.description'),
      "url": "https://flashimage.fun",
      "applicationCategory": "MultimediaApplication",
      "operatingSystem": "Web",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": 4.8,
        "bestRating": 5,
        "worstRating": 1,
        "ratingCount": 2847
      }
    }}
  />

  <Header />
  <main class="page-transition">
    <div class="stagger-animation">
      <Hero />
      <Stats />
      <ImageCarousel />
      <KeyFeatures />
      <Testimonials />
      <Limitations />
      <SafetyResponsibility />
      <TryGemini />
    </div>
  </main>
  <Footer />
</BaseLayout>
