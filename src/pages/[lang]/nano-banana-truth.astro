---
import BaseLayout from "../../layouts/BaseLayout.astro";
import Header from "../../components/layout/Header.astro";
import Footer from "../../components/layout/Footer.astro";
import Timeline from "../../components/sections/Timeline.astro";
import { getLangFromUrl, useTranslations, isValidLang } from "../../i18n";

// 获取语言参数
const { lang } = Astro.params;

// 验证语言参数
if (!lang || !isValidLang(lang)) {
  return Astro.redirect("/en/nano-banana-truth");
}

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

const pageTitle = t("pages.nanoBananaTruth.title");
const pageDescription = t("pages.nanoBananaTruth.description");

// 生成静态路径 - 动态支持所有启用的语言
export async function getStaticPaths() {
  const { getEnabledLanguageCodes } = await import("../../config/languages");
  const enabledLanguages = getEnabledLanguageCodes();

  return enabledLanguages.map((lang) => ({
    params: { lang },
  }));
}
---

<BaseLayout title={pageTitle} description={pageDescription}>
  <Header />
  <main
    class="min-h-screen bg-gradient-to-b from-gray-900 via-black to-gray-900"
  >
    <!-- Hero Section -->
    <section class="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-6xl mx-auto text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          <span
            class="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent"
          >
            {t("nanoBanana.hero.title")}
          </span>
        </h1>
        <p class="text-xl text-gray-400 max-w-4xl mx-auto leading-relaxed mb-8">
          {t("nanoBanana.hero.subtitle")}
        </p>
        <div class="flex flex-wrap justify-center gap-4">
          <span
            class="px-4 py-2 bg-yellow-500/20 text-yellow-300 rounded-full text-sm"
          >
            {t("nanoBanana.hero.tags.betaLegend")}
          </span>
          <span
            class="px-4 py-2 bg-orange-500/20 text-orange-300 rounded-full text-sm"
          >
            {t("nanoBanana.hero.tags.communityBuzz")}
          </span>
          <span
            class="px-4 py-2 bg-red-500/20 text-red-300 rounded-full text-sm"
          >
            {t("nanoBanana.hero.tags.censorshipControversy")}
          </span>
        </div>
      </div>
    </section>

    <!-- Timeline Section -->
    <Timeline />

    <!-- Community Impact Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-6xl mx-auto">
        <h2 class="text-3xl font-bold text-white mb-12 text-center">
          {t("nanoBanana.communityImpact.title")}
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div class="bg-gray-800/50 border border-gray-700/50 rounded-xl p-8">
            <h3 class="text-2xl font-bold text-green-400 mb-6">
              {t("nanoBanana.communityImpact.betaGloryDays.title")}
            </h3>
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div
                  class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"
                >
                </div>
                <div>
                  <h4 class="font-semibold text-white">
                    {
                      t(
                        "nanoBanana.communityImpact.betaGloryDays.redditBuzz.title",
                      )
                    }
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {
                      t(
                        "nanoBanana.communityImpact.betaGloryDays.redditBuzz.description",
                      )
                    }
                  </p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div
                  class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"
                >
                </div>
                <div>
                  <h4 class="font-semibold text-white">
                    {
                      t(
                        "nanoBanana.communityImpact.betaGloryDays.lmArenaRankings.title",
                      )
                    }
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {
                      t(
                        "nanoBanana.communityImpact.betaGloryDays.lmArenaRankings.description",
                      )
                    }
                  </p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div
                  class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"
                >
                </div>
                <div>
                  <h4 class="font-semibold text-white">
                    {
                      t(
                        "nanoBanana.communityImpact.betaGloryDays.developerAdoption.title",
                      )
                    }
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {
                      t(
                        "nanoBanana.communityImpact.betaGloryDays.developerAdoption.description",
                      )
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gray-800/50 border border-gray-700/50 rounded-xl p-8">
            <h3 class="text-2xl font-bold text-red-400 mb-6">
              {t("nanoBanana.communityImpact.officialControversy.title")}
            </h3>
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0">
                </div>
                <div>
                  <h4 class="font-semibold text-white">
                    {
                      t(
                        "nanoBanana.communityImpact.officialControversy.overModeration.title",
                      )
                    }
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {
                      t(
                        "nanoBanana.communityImpact.officialControversy.overModeration.description",
                      )
                    }
                  </p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0">
                </div>
                <div>
                  <h4 class="font-semibold text-white">
                    {
                      t(
                        "nanoBanana.communityImpact.officialControversy.communityDisappointment.title",
                      )
                    }
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {
                      t(
                        "nanoBanana.communityImpact.officialControversy.communityDisappointment.description",
                      )
                    }
                  </p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0">
                </div>
                <div>
                  <h4 class="font-semibold text-white">
                    {
                      t(
                        "nanoBanana.communityImpact.officialControversy.featureRegression.title",
                      )
                    }
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {
                      t(
                        "nanoBanana.communityImpact.officialControversy.featureRegression.description",
                      )
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Future Outlook Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-3xl font-bold text-white mb-8">
          {t("nanoBanana.futureOutlook.title")}
        </h2>
        <div class="bg-gray-800/50 border border-gray-700/50 rounded-xl p-8">
          <p class="text-xl text-gray-300 leading-relaxed mb-6">
            {t("nanoBanana.futureOutlook.description")}
          </p>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
            <div class="bg-gray-700/30 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-blue-400 mb-3">
                {t("nanoBanana.futureOutlook.shortTerm.title")}
              </h3>
              <ul class="text-gray-300 text-sm space-y-2">
                <li>
                  • {t("nanoBanana.futureOutlook.shortTerm.optimizeModeration")}
                </li>
                <li>
                  • {t("nanoBanana.futureOutlook.shortTerm.improveQuality")}
                </li>
                <li>
                  • {t("nanoBanana.futureOutlook.shortTerm.enhanceStability")}
                </li>
              </ul>
            </div>
            <div class="bg-gray-700/30 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-purple-400 mb-3">
                {t("nanoBanana.futureOutlook.longTerm.title")}
              </h3>
              <ul class="text-gray-300 text-sm space-y-2">
                <li>
                  • {t("nanoBanana.futureOutlook.longTerm.strongerCreativity")}
                </li>
                <li>
                  • {t("nanoBanana.futureOutlook.longTerm.betterExperience")}
                </li>
                <li>
                  • {t("nanoBanana.futureOutlook.longTerm.broaderApplications")}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-3xl font-bold text-white mb-8">
          {t("nanoBanana.callToAction.title")}
        </h2>
        <p class="text-xl text-gray-400 mb-8">
          {t("nanoBanana.callToAction.description")}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="https://gemini.google.com"
            target="_blank"
            rel="noopener noreferrer"
            class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-3 rounded-full font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            {t("nanoBanana.callToAction.tryInGemini")}
          </a>
          <a
            href="https://aistudio.google.com"
            target="_blank"
            rel="noopener noreferrer"
            class="border border-gray-600 text-gray-300 px-8 py-3 rounded-full font-medium hover:border-gray-500 hover:text-white transition-all duration-200"
          >
            {t("nanoBanana.callToAction.tryInAiStudio")}
          </a>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</BaseLayout>
