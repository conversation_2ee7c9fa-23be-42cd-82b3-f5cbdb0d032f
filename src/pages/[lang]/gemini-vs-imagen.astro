---
import BaseLayout from '../../layouts/BaseLayout.astro';
import Header from '../../components/layout/Header.astro';
import Footer from '../../components/layout/Footer.astro';
import ComparisonTable from '../../components/sections/ComparisonTable.astro';
import { getLangFromUrl, useTranslations, isValidLang } from '../../i18n';

// 获取语言参数
const { lang } = Astro.params;

// 验证语言参数
if (!lang || !isValidLang(lang)) {
  return Astro.redirect('/en/gemini-vs-imagen');
}

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

const pageTitle = t('pages.geminiVsImagen.title');
const pageDescription = t('pages.geminiVsImagen.description');

// 生成静态路径 - 动态支持所有启用的语言
export async function getStaticPaths() {
  const { getEnabledLanguageCodes } = await import('../../config/languages');
  const enabledLanguages = getEnabledLanguageCodes();

  return enabledLanguages.map(lang => ({
    params: { lang }
  }));
}
---

<BaseLayout title={pageTitle} description={pageDescription}>
  <Header />
  <main class="min-h-screen bg-gradient-to-b from-gray-900 via-black to-gray-900">
    <!-- Hero Section -->
    <section class="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-6xl mx-auto text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          <span class="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
            {t('comparison.hero.title')}
          </span>
        </h1>
        <p class="text-xl text-gray-400 max-w-4xl mx-auto leading-relaxed mb-8">
          {t('comparison.hero.subtitle')}
        </p>
        <div class="flex flex-wrap justify-center gap-4">
          <span class="px-4 py-2 bg-blue-500/20 text-blue-300 rounded-full text-sm">
            {t('comparison.hero.tags.characterConsistency')}
          </span>
          <span class="px-4 py-2 bg-purple-500/20 text-purple-300 rounded-full text-sm">
            {t('comparison.hero.tags.textRendering')}
          </span>
          <span class="px-4 py-2 bg-pink-500/20 text-pink-300 rounded-full text-sm">
            {t('comparison.hero.tags.costAnalysis')}
          </span>
          <span class="px-4 py-2 bg-green-500/20 text-green-300 rounded-full text-sm">
            {t('comparison.hero.tags.practicalTests')}
          </span>
        </div>
      </div>
    </section>

    <!-- Comparison Table Section -->
    <ComparisonTable />

    <!-- Detailed Analysis Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-6xl mx-auto">
        <h2 class="text-3xl font-bold text-white mb-12 text-center">
          {t('comparison.detailedAnalysis.title')}
        </h2>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Gemini Strengths -->
          <div class="bg-gradient-to-br from-blue-900/30 to-purple-900/30 border border-blue-500/30 rounded-xl p-8">
            <h3 class="text-2xl font-bold text-blue-400 mb-6">
              {t('comparison.detailedAnalysis.geminiStrengths.title')}
            </h3>
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 class="font-semibold text-white">
                    {t('comparison.detailedAnalysis.geminiStrengths.characterConsistency.title')}
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {t('comparison.detailedAnalysis.geminiStrengths.characterConsistency.description')}
                  </p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 class="font-semibold text-white">
                    {t('comparison.detailedAnalysis.geminiStrengths.conversationalEditing.title')}
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {t('comparison.detailedAnalysis.geminiStrengths.conversationalEditing.description')}
                  </p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 class="font-semibold text-white">
                    {t('comparison.detailedAnalysis.geminiStrengths.multiImageFusion.title')}
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {t('comparison.detailedAnalysis.geminiStrengths.multiImageFusion.description')}
                  </p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 class="font-semibold text-white">
                    {t('comparison.detailedAnalysis.geminiStrengths.freeToUse.title')}
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {t('comparison.detailedAnalysis.geminiStrengths.freeToUse.description')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Imagen Strengths -->
          <div class="bg-gradient-to-br from-orange-900/30 to-red-900/30 border border-orange-500/30 rounded-xl p-8">
            <h3 class="text-2xl font-bold text-orange-400 mb-6">
              {t('comparison.detailedAnalysis.imagenStrengths.title')}
            </h3>
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 class="font-semibold text-white">
                    {t('comparison.detailedAnalysis.imagenStrengths.textRendering.title')}
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {t('comparison.detailedAnalysis.imagenStrengths.textRendering.description')}
                  </p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 class="font-semibold text-white">
                    {t('comparison.detailedAnalysis.imagenStrengths.styleTransfer.title')}
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {t('comparison.detailedAnalysis.imagenStrengths.styleTransfer.description')}
                  </p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 class="font-semibold text-white">
                    {t('comparison.detailedAnalysis.imagenStrengths.worldKnowledge.title')}
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {t('comparison.detailedAnalysis.imagenStrengths.worldKnowledge.description')}
                  </p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 class="font-semibold text-white">
                    {t('comparison.detailedAnalysis.imagenStrengths.balancedModeration.title')}
                  </h4>
                  <p class="text-gray-300 text-sm">
                    {t('comparison.detailedAnalysis.imagenStrengths.balancedModeration.description')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Conclusion Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-3xl font-bold text-white mb-8">
          {t('comparison.conclusion.title')}
        </h2>
        <div class="bg-gray-800/50 border border-gray-700/50 rounded-xl p-8">
          <p class="text-xl text-gray-300 leading-relaxed mb-6">
            {t('comparison.conclusion.summary')}
          </p>
          <p class="text-lg text-gray-400">
            {t('comparison.conclusion.recommendation')}
          </p>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</BaseLayout>
