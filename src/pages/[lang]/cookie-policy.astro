---
import BaseLayout from '../../layouts/BaseLayout.astro';
import Header from '../../components/layout/Header.astro';
import Footer from '../../components/layout/Footer.astro';
import { useTranslations, isValidLang } from '../../i18n';

// 获取语言参数
const { lang } = Astro.params;

// 验证语言参数
if (!lang || !isValidLang(lang)) {
  return Astro.redirect('/en/cookie-policy');
}

const t = useTranslations(lang);

const title = t('cookiePolicy.title');
const description = t('cookiePolicy.subtitle');

// 生成静态路径 - 动态支持所有启用的语言
export async function getStaticPaths() {
  // 动态导入语言配置
  const { getEnabledLanguageCodes } = await import('../../config/languages');
  const enabledLanguages = getEnabledLanguageCodes();

  return enabledLanguages.map(lang => ({
    params: { lang }
  }));
}
---

<BaseLayout title={title} description={description}>
  <Header />
  <main class="min-h-screen bg-black text-white">
    <!-- Hero Section -->
    <section class="pt-32 pb-16 bg-gradient-to-b from-gray-900 to-black">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
            {t('cookiePolicy.title')}
          </h1>
          <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            {t('cookiePolicy.subtitle')}
          </p>
          <p class="text-sm text-gray-500">
            {t('cookiePolicy.lastUpdated')}
          </p>
        </div>
      </div>
    </section>

    <!-- Content Section -->
    <section class="py-16">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="prose prose-invert prose-lg max-w-none">
          
          <!-- What Are Cookies -->
          <div class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-4">
              {t('cookiePolicy.sections.whatAreCookies.title')}
            </h2>
            <p class="text-gray-300 leading-relaxed">
              {t('cookiePolicy.sections.whatAreCookies.content')}
            </p>
          </div>

          <!-- How We Use Cookies -->
          <div class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-4">
              {t('cookiePolicy.sections.howWeUseCookies.title')}
            </h2>
            <p class="text-gray-300 leading-relaxed">
              {t('cookiePolicy.sections.howWeUseCookies.content')}
            </p>
          </div>

          <!-- Types of Cookies -->
          <div class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-4">
              {t('cookiePolicy.sections.typesOfCookies.title')}
            </h2>
            <p class="text-gray-300 leading-relaxed">
              {t('cookiePolicy.sections.typesOfCookies.content')}
            </p>
          </div>

          <!-- Managing Cookies -->
          <div class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-4">
              {t('cookiePolicy.sections.managingCookies.title')}
            </h2>
            <p class="text-gray-300 leading-relaxed">
              {t('cookiePolicy.sections.managingCookies.content')}
            </p>
          </div>

          <!-- Third Party Cookies -->
          <div class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-4">
              {t('cookiePolicy.sections.thirdPartyCookies.title')}
            </h2>
            <p class="text-gray-300 leading-relaxed">
              {t('cookiePolicy.sections.thirdPartyCookies.content')}
            </p>
          </div>

          <!-- Contact -->
          <div class="mb-12">
            <h2 class="text-2xl font-bold text-white mb-4">
              {t('cookiePolicy.sections.contact.title')}
            </h2>
            <p class="text-gray-300 leading-relaxed">
              {t('cookiePolicy.sections.contact.content')}
            </p>
          </div>

        </div>
      </div>
    </section>

    <!-- Back to Home -->
    <section class="py-16 border-t border-gray-800">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <a 
          href={`/${lang}`}
          class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Home
        </a>
      </div>
    </section>
  </main>
  <Footer />
</BaseLayout>
