---
import BaseLayout from "../../layouts/BaseLayout.astro";
import Header from "../../components/layout/Header.astro";
import Footer from "../../components/layout/Footer.astro";
import { getLangFromUrl, useTranslations, isValidLang } from "../../i18n";

// 获取语言参数
const { lang } = Astro.params;

// 验证语言参数
if (!lang || !isValidLang(lang)) {
  return Astro.redirect("/en/developer-guide");
}

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

const pageTitle = t("pages.developerGuide.title");
const pageDescription = t("pages.developerGuide.description");

// 生成静态路径 - 动态支持所有启用的语言
export async function getStaticPaths() {
  const { getEnabledLanguageCodes } = await import("../../config/languages");
  const enabledLanguages = getEnabledLanguageCodes();

  return enabledLanguages.map((lang) => ({
    params: { lang },
  }));
}
---

<BaseLayout title={pageTitle} description={pageDescription}>
  <Header />
  <main
    class="min-h-screen bg-gradient-to-b from-gray-900 via-black to-gray-900"
  >
    <!-- Hero Section -->
    <section class="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          <span
            class="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent"
          >
            {t("pages.developerGuide.hero.title")}
          </span>
        </h1>
        <p class="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
          {t("pages.developerGuide.hero.description")}
        </p>
        <div class="flex flex-wrap justify-center gap-4 mt-8">
          <span
            class="px-4 py-2 bg-blue-500/20 text-blue-300 rounded-full text-sm"
          >
            {t("pages.developerGuide.hero.tags.apiIntegration")}
          </span>
          <span
            class="px-4 py-2 bg-purple-500/20 text-purple-300 rounded-full text-sm"
          >
            {t("pages.developerGuide.hero.tags.codeExamples")}
          </span>
          <span
            class="px-4 py-2 bg-pink-500/20 text-pink-300 rounded-full text-sm"
          >
            {t("pages.developerGuide.hero.tags.bestPractices")}
          </span>
          <span
            class="px-4 py-2 bg-green-500/20 text-green-300 rounded-full text-sm"
          >
            {t("pages.developerGuide.hero.tags.practicalTutorials")}
          </span>
        </div>
      </div>
    </section>

    <!-- Quick Start Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto">
        <h2 class="text-3xl font-bold text-white mb-8">
          {t("pages.developerGuide.quickStart.title")}
        </h2>
        <div class="bg-gray-800/50 border border-gray-700/50 rounded-xl p-8">
          <h3 class="text-xl font-semibold text-white mb-4">
            {t("pages.developerGuide.quickStart.steps.getApiKey.title")}
          </h3>
          <p class="text-gray-300 mb-6">
            {t("pages.developerGuide.quickStart.steps.getApiKey.description")}
          </p>

          <h3 class="text-xl font-semibold text-white mb-4">
            {t("pages.developerGuide.quickStart.steps.installSdk.title")}
          </h3>
          <div
            class="bg-gray-900/50 border border-gray-700/50 rounded-lg p-4 mb-6"
          >
            <pre
              class="text-sm text-gray-300"><code>npm install @google/generative-ai</code></pre>
          </div>

          <h3 class="text-xl font-semibold text-white mb-4">
            {t("pages.developerGuide.quickStart.steps.basicUsage.title")}
          </h3>
          <div class="bg-gray-900/50 border border-gray-700/50 rounded-lg p-4">
            <pre
              class="text-sm text-gray-300"><code>{`import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash-image" });

const result = await model.generateContent([
  "${t('pages.developerGuide.quickStart.steps.basicUsage.prompt')}",
  // ${t('pages.developerGuide.quickStart.steps.basicUsage.codeComment')}
]);

console.log(result.response.text());`}</code></pre>
          </div>
        </div>
      </div>
    </section>

    <!-- Resources Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-3xl font-bold text-white mb-8">
          {t("pages.developerGuide.resources.title")}
        </h2>
        <p class="text-xl text-gray-400 mb-12">
          {t("pages.developerGuide.resources.description")}
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <a
            href={`/${currentLang}/gemini-vs-imagen`}
            class="group bg-gray-800/50 border border-gray-700/50 rounded-xl p-8 hover:border-blue-500/50 transition-all duration-300"
          >
            <h3
              class="text-xl font-semibold text-white mb-4 group-hover:text-blue-400 transition-colors"
            >
              {t("pages.developerGuide.resources.modelComparison.title")}
            </h3>
            <p class="text-gray-400">
              {t("pages.developerGuide.resources.modelComparison.description")}
            </p>
          </a>

          <a
            href={`/${currentLang}/nano-banana-truth`}
            class="group bg-gray-800/50 border border-gray-700/50 rounded-xl p-8 hover:border-purple-500/50 transition-all duration-300"
          >
            <h3
              class="text-xl font-semibold text-white mb-4 group-hover:text-purple-400 transition-colors"
            >
              {t("pages.developerGuide.resources.nanoBananaStory.title")}
            </h3>
            <p class="text-gray-400">
              {t("pages.developerGuide.resources.nanoBananaStory.description")}
            </p>
          </a>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</BaseLayout>
