---
import LanguageDetector from '../components/seo/LanguageDetector.astro';
import BaseLayout from '../layouts/BaseLayout.astro';
import Header from '../components/layout/Header.astro';
import Footer from '../components/layout/Footer.astro';
import StructuredData from '../components/seo/StructuredData.astro';
import { getEnabledLanguages } from '../config/languages';

// 获取所有启用的语言用于显示
const enabledLanguages = getEnabledLanguages();
---

<BaseLayout
  title="Flash Image Fun - AI Image Generation & Editing Platform"
  description="Create stunning images with AI technology. Advanced image generation, editing, and enhancement tools for creators, designers, and businesses."
  type="website"
  canonical="https://flashimage.fun/"
>
  <StructuredData
    type="SoftwareApplication"
    title="Flash Image Fun"
    description="AI-powered image generation and editing platform for creators, designers, and businesses"
    url="https://flashimage.fun"
    applicationCategory="MultimediaApplication"
    operatingSystem="Web"
    offers={{
      price: "0",
      priceCurrency: "USD",
      availability: "https://schema.org/InStock"
    }}
    rating={{
      ratingValue: 4.8,
      bestRating: 5,
      worstRating: 1,
      ratingCount: 2847
    }}
  />

  <LanguageDetector />

  <Header />

  <main class="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
    <div class="container mx-auto px-4 py-16">
      <!-- Hero Section -->
      <div class="text-center mb-16">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
          Flash Image Fun
        </h1>

        <p class="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
          AI-Powered Image Generation & Editing Platform
        </p>

        <p class="text-gray-400 mb-12 max-w-2xl mx-auto">
          Choose your preferred language to get started with our powerful AI image tools
        </p>
      </div>

      <!-- Language Selection Grid -->
      <div class="max-w-6xl mx-auto">
        <h2 class="text-2xl font-semibold text-white text-center mb-8">Select Your Language</h2>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {enabledLanguages.map((lang) => (
            <a
              href={`/${lang.code}`}
              class="group bg-gray-800/50 hover:bg-gray-700/50 border border-gray-700 hover:border-blue-500 rounded-xl p-4 transition-all duration-300 hover:scale-105 hover:shadow-xl"
            >
              <div class="text-3xl mb-3 text-center">{lang.flag}</div>
              <div class="text-white font-medium text-center text-sm">{lang.name}</div>
              <div class="text-gray-400 text-xs text-center mt-1">{lang.englishName}</div>
            </a>
          ))}
        </div>
      </div>

      <!-- Features Preview -->
      <div class="mt-20 text-center">
        <h2 class="text-3xl font-bold text-white mb-8">Why Choose Flash Image Fun?</h2>

        <div class="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div class="bg-gray-800/30 rounded-xl p-6 border border-gray-700">
            <div class="text-4xl mb-4">🎨</div>
            <h3 class="text-xl font-semibold text-white mb-3">AI Image Generation</h3>
            <p class="text-gray-400">Create stunning images from text descriptions using advanced AI technology</p>
          </div>

          <div class="bg-gray-800/30 rounded-xl p-6 border border-gray-700">
            <div class="text-4xl mb-4">✨</div>
            <h3 class="text-xl font-semibold text-white mb-3">Smart Editing</h3>
            <p class="text-gray-400">Enhance and modify images with intelligent editing tools</p>
          </div>

          <div class="bg-gray-800/30 rounded-xl p-6 border border-gray-700">
            <div class="text-4xl mb-4">🌍</div>
            <h3 class="text-xl font-semibold text-white mb-3">26 Languages</h3>
            <p class="text-gray-400">Available in 26 languages for users worldwide</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <Footer />

  <!-- Enhanced JavaScript for better UX -->
  <script>
    // Enhanced language selection with analytics
    document.addEventListener('DOMContentLoaded', function() {
      // Track language selection clicks
      const languageLinks = document.querySelectorAll('a[href^="/"]') as NodeListOf<HTMLAnchorElement>;

      languageLinks.forEach(link => {
        link.addEventListener('click', function() {
          const href = this.getAttribute('href');
          const langMatch = href?.match(/^\/([a-z]{2})/);

          if (langMatch) {
            const selectedLang = langMatch[1];

            // Save user preference
            localStorage.setItem('preferred-language', selectedLang);

            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            this.style.opacity = '0.8';

            // Optional: Track analytics
            if (typeof (window as any).gtag !== 'undefined') {
              (window as any).gtag('event', 'language_selection', {
                'language': selectedLang,
                'page_location': window.location.href
              });
            }
          }
        });
      });

      // Add hover effects for better interactivity
      const cards = document.querySelectorAll('a[href^="/"]') as NodeListOf<HTMLAnchorElement>;
      cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-2px)';
        });

        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0)';
        });
      });
    });
  </script>

  <!-- Fallback for no JavaScript -->
  <noscript>
    <style>
      .language-detector { display: none; }
      .main-content { display: block !important; }
    </style>
  </noscript>
</BaseLayout>
