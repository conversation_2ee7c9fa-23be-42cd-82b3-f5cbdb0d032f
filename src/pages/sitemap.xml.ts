import type { APIRoute } from 'astro';
import { getEnabledLanguageCodes } from '../config/languages';

// 网站基础URL
const SITE_URL = 'https://flashimage.fun';

// 定义所有页面路由
const routes = [
  '',  // 首页
  'developer-guide',
  'gemini-vs-imagen', 
  'nano-banana-truth'
];

// 获取支持的语言列表
const supportedLanguages = getEnabledLanguageCodes();

// 生成sitemap XML
export const GET: APIRoute = () => {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" 
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${generateSitemapEntries()}
</urlset>`;

  return new Response(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });
};

function generateSitemapEntries(): string {
  const entries: string[] = [];
  const now = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

  // 为每个路由生成多语言条目
  routes.forEach(route => {
    supportedLanguages.forEach(lang => {
      const url = route === '' 
        ? `${SITE_URL}/${lang}`
        : `${SITE_URL}/${lang}/${route}`;
      
      // 生成该页面的所有语言版本的 hreflang 链接
      const hreflangLinks = supportedLanguages.map(hrefLang => {
        const hrefUrl = route === ''
          ? `${SITE_URL}/${hrefLang}`
          : `${SITE_URL}/${hrefLang}/${route}`;
        return `    <xhtml:link rel="alternate" hreflang="${hrefLang}" href="${hrefUrl}" />`;
      }).join('\n');

      entries.push(`  <url>
    <loc>${url}</loc>
    <lastmod>${now}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${getPriority(route)}</priority>
${hreflangLinks}
  </url>`);
    });
  });

  return entries.join('\n');
}

function getPriority(route: string): string {
  switch (route) {
    case '': return '1.0';  // Homepage highest priority
    case 'developer-guide': return '0.8';  // Developer guide high priority
    case 'gemini-vs-imagen': return '0.8';  // Model comparison high priority
    case 'nano-banana-truth': return '0.6';  // Other pages medium priority
    default: return '0.5';
  }
}
