import type { APIRoute } from 'astro';

const SITE_URL = 'https://flashimage.fun';

export const GET: APIRoute = () => {
  const robotsTxt = `User-agent: *
Allow: /

# 优化爬取
Crawl-delay: 1

# Sitemap
Sitemap: ${SITE_URL}/sitemap.xml

# 禁止爬取的路径（如果有的话）
# Disallow: /admin/
# Disallow: /api/

# 允许所有主要搜索引擎
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /

User-agent: DuckDuckBot
Allow: /

User-agent: Bai<PERSON>pider
Allow: /`;

  return new Response(robotsTxt, {
    headers: {
      'Content-Type': 'text/plain',
    },
  });
};
