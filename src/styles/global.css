@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    scroll-behavior: smooth;
    background: #0f0f0f;
  }
  
  body {
    margin: 0;
    padding: 0;
    line-height: 1.6;
    background: linear-gradient(180deg, #0f0f0f 0%, #1a1a1a 100%);
    min-height: 100vh;
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #1a1a1a;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-full hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105;
  }
  
  .btn-secondary {
    @apply inline-flex items-center justify-center px-6 py-3 border-2 border-gray-600 text-gray-300 font-semibold rounded-full hover:border-gray-400 hover:text-white transition-all duration-200;
  }
  
  .card-dark {
    @apply bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl p-6 border border-gray-700/50 hover:shadow-2xl transition-all duration-300;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent;
  }
}

/* Custom utility classes */
@layer utilities {
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  
  .animation-delay-4000 {
    animation-delay: 4s;
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
}
