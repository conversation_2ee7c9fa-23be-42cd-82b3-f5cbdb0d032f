{"site": {"title": "Flash Image Fun", "description": "Create and edit images with AI - Bring your imagination to life with advanced image generation and editing capabilities", "contact": "<EMAIL>"}, "nav": {"home": "<PERSON><PERSON><PERSON>", "features": "<PERSON><PERSON><PERSON>", "developerGuide": "Udviklerguide", "modelComparison": "<PERSON><PERSON><PERSON><PERSON><PERSON>ng", "nanoBananaTruth": "Sandheden om nano-banana", "testimonials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "about": "Om"}, "meta": {"title": "Gemini 2.5 Flash Image: Næste generations AI-billedgenerator – Karakterkonsistens og samtalebaseret redigering", "description": "Oplev de banebrydende muligheder for AI-billedgenerering i Gemini 2.5 Flash Image: brancheførende karakterkonsistens, samtalebaseret redigering og multi‑billedfusion. Komplet udviklervejledning, API-integration og praktiske eksempler."}, "hero": {"title": "<PERSON><PERSON> og rediger billeder med Gemini", "subtitle": "<PERSON><PERSON><PERSON> din fantasi levende", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> og rediger billeder med enkle tekstprompter, eller kombiner flere billeder for at skabe noget nyt. Alt i Gemini.", "cta": {"primary": "Prøv i Gemini", "secondary": "Prøv i Google AI Studio"}, "stats": {"aiPowered": "AI‑drevet", "aiPoweredDesc": "<PERSON><PERSON>ret teknologi", "unlimited": "Ubegrænset", "unlimitedDesc": "Kreative muligheder", "instant": "Øjeblikkelige", "instantDesc": "Resultater"}}, "features": {"consistency": {"title": "Hold figurerne konsist<PERSON>", "description": "<PERSON><PERSON><PERSON> k<PERSON> for figurer på tværs af flere billeder og scener"}, "boundaries": {"title": "<PERSON><PERSON><PERSON> designgræ<PERSON>", "description": "Udforsk kreative muligheder med avancerede AI‑drevne designværktøjer"}, "possibilities": {"title": "<PERSON><PERSON> prompt, mange muligheder", "description": "<PERSON><PERSON><PERSON> flere billeder med kun én prompt for at udforske forskellige kreative retninger. <PERSON>r skab flere billeder, der arbejder sammen om at fortælle en komplet historie."}}, "capabilities": {"title": "Kraftfulde AI‑billedfunktioner", "subtitle": "<PERSON><PERSON>, hvad der er muligt med avanceret billedgenerering"}, "keyFeatures": {"title": "Nøglefunktioner", "items": {"multimodalUnderstanding": {"title": "Multimodal forståelse", "description": "Upload billeder og del tekstinstruktioner med Gemini for at skabe komplekse og detaljerede billeder."}, "conversationalInputs": {"title": "Samtaleinput", "description": "B<PERSON> hverdagssprog, mens du skaber billeder, og fortsæt samtalen for at finjustere det, modellen genererer."}, "realWorldKnowledge": {"title": "Virkelighedsviden", "description": "<PERSON><PERSON><PERSON>, der følger virkelighedens logik, takket være Geminis avancerede ræsonneringsevner."}}}, "carousel": {"sections": [{"title": "Hold figurerne konsist<PERSON>", "description": "<PERSON><PERSON><PERSON> de samme figurer, mens du ændrer deres påkl<PERSON>dning, positurer, lyssæ<PERSON>ning eller scene. Eller genopfind dig selv – på tværs af årtier, på forskellige steder eller i dit barndomsdrømmejob.", "images": [{"title": "Remove the helmet", "description": "Character consistency demonstration", "prompt": "Remove the helmet", "src": "/images/carousel/character-consistency/image-1.jpg", "alt": "AI generated character portrait showing consistent design"}, {"title": "Multiple character edits", "description": "Same character, multiple changes", "prompt": "Remove the door mirror. Make the landscape snowy and mountainous. Make her hair dyed cool blond at the top and magenta at the bottom. She is wearing a yellow and dark blue flannel shirt", "src": "/images/carousel/character-consistency/image-2.jpg", "alt": "Same AI character with multiple edits applied"}, {"title": "1960s recording studio scene", "description": "Character in vintage setting", "prompt": "A classic, faded photograph capturing a scene from a 1960s recording studio, featuring these two blue characters. They are depicted in the control room, surrounded by the warm glow of vacuum tubes and the complex array of a large-format mixing console.", "src": "/images/carousel/character-consistency/image-3.jpg", "alt": "Characters in 1960s recording studio setting"}, {"title": "Red flower headpiece", "description": "Character accessory change", "prompt": "Change head piece to something made from red flowers", "src": "/images/carousel/character-consistency/image-4.jpg", "alt": "Character with red flower headpiece"}]}, {"title": "Prompt, kombiner, skab", "description": "Flet op til tre billeder for at skabe noget nyt. Generér surrealistisk kunst, kombiner forskellige fotoelementer eller bland objekter, farver og teksturer sømløst.", "images": [{"title": "Hyper-detailed bubble photograph", "description": "Surrealist art creation", "prompt": "A hyper-detailed, high-fashion photograph capturing a woman floating within a massive, amorphous bubble of translucent, glass-like liquid on light blue background.", "src": "/images/carousel/prompt-combine-create/image-1.jpg", "alt": "Woman floating in translucent bubble"}, {"title": "Image remix", "description": "Combining multiple images", "prompt": "Remix these 2 images", "src": "/images/carousel/prompt-combine-create/image-2.jpg", "alt": "Creative remix of multiple images"}, {"title": "Astronaut scene combination", "description": "Complex image merging", "prompt": "Replace the astronaut on the right with the women and remove the helmet on the astronaut on the left to show the man's face. The two are looking at each other.", "src": "/images/carousel/prompt-combine-create/image-3.jpg", "alt": "Astronaut scene with character replacement"}, {"title": "Banana lightbulb concept", "description": "Creative object combination", "prompt": "A Banana that peels to reveal a lightbulb", "src": "/images/carousel/prompt-combine-create/image-4.jpg", "alt": "<PERSON><PERSON> revealing lightbulb inside"}]}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Opret og redigér billeder med kraftfuld kontrol. Udskift baggrunden, genskab falmede billeder og skift figurers tøj. Bliv ved med at justere med naturligt sprog, indtil du er tilfreds.", "images": [{"title": "Synchronized swimmers in lotus", "description": "Precise image combination", "prompt": "Combine these photos so that the synchronized swimmers are inside the lotus flower", "src": "/images/carousel/control-details/image-1.jpg", "alt": "Synchronized swimmers inside lotus flower"}, {"title": "Man with dog", "description": "Character interaction control", "prompt": "The man cuddles with his dog", "src": "/images/carousel/control-details/image-2.jpg", "alt": "Man cuddling with his dog"}, {"title": "Polaroid headshots from 1980s", "description": "Time period styling", "prompt": "Create 5 headshot polaroid prints, laid out on a clean table, all of which show me in various situations from the 1980's", "src": "/images/carousel/control-details/image-3.jpg", "alt": "1980s style polaroid headshots"}, {"title": "Underwater scene transformation", "description": "Background replacement", "prompt": "Make woman underwater, and remove the couch and wallpaper", "src": "/images/carousel/control-details/image-4.jpg", "alt": "Woman in underwater scene"}]}, {"title": "<PERSON><PERSON><PERSON> designgræ<PERSON>", "description": "Eksperimentér med kreative retninger eller sæt dem i nye kontekster. Anvend specifikke mønstre på synlige overflader, eller afprøv farver til mode, design og indretning.", "images": [{"title": "Romantisk snow scene", "description": "Creative scene transformation", "prompt": "A close-up shot of a romantic moment holding each other while it snows", "src": "/images/carousel/design-boundaries/image-1.jpg", "alt": "Romantisk couple in snowy scene"}, {"title": "Career transformations", "description": "Character in different professions", "prompt": "Show this man as a teacher. Show this man as a sculptor. Show this man as a nurse. Show this man as a baker", "src": "/images/carousel/design-boundaries/image-2.jpg", "alt": "Man in various professional roles"}, {"title": "16-bit video game character", "description": "Style transformation", "prompt": "Recreate this dog as a 16-Bit Video Game character, and place the character in a level of a 2d 16-bit platform video game", "src": "/images/carousel/design-boundaries/image-3.jpg", "alt": "<PERSON> as 16-bit video game character"}, {"title": "T-rex costume variations", "description": "Creative costume design", "prompt": "The t-rex is in a halloween costume. Now try a more fun costume. Fun. Now let's try a cute costume. How about a pirate costume?", "src": "/images/carousel/design-boundaries/image-4.jpg", "alt": "T-rex in various costumes"}]}, {"title": "One prompt, many possibilities", "description": "Generate multiple images using just one prompt to explore different creative avenues. Or create several images that work together to tell a complete story.", "images": [{"title": "First interpretation", "description": "Initial creative direction", "prompt": "Generate multiple variations of this concept", "src": "/images/carousel/multiple-possibilities/image-1.jpg", "alt": "First AI interpretation of prompt"}, {"title": "Alternative approach", "description": "Different creative angle", "prompt": "Same prompt, different style approach", "src": "/images/carousel/multiple-possibilities/image-2.jpg", "alt": "Alternative AI approach to same prompt"}, {"title": "Creative twist", "description": "Unique interpretation", "prompt": "Creative variation on the original concept", "src": "/images/carousel/multiple-possibilities/image-3.jpg", "alt": "Creative AI twist on prompt"}, {"title": "Unique perspective", "description": "Final variation", "prompt": "Completely different take on the same idea", "src": "/images/carousel/multiple-possibilities/image-4.jpg", "alt": "Unique AI perspective on prompt"}]}, {"title": "Benchmark-resultater", "description": "Gemini 2.5 Flash Image viser stærk ydeevne på tværs af forskellige benchmarks for billedgenerering og -redigering med konkurrencedygtige resultater for kvalitet, nøjagtighed og brugertilfredshed.", "images": [{"title": "Quality benchmark results", "description": "Performance metrics comparison", "prompt": "Benchmark evaluation showing quality metrics", "src": "/images/carousel/benchmarks/image-1.jpg", "alt": "Quality benchmark comparison chart"}, {"title": "User satisfaction scores", "description": "User preference studies", "prompt": "User satisfaction evaluation results", "src": "/images/carousel/benchmarks/image-2.jpg", "alt": "User satisfaction benchmark results"}, {"title": "Speed and efficiency", "description": "Performance speed comparison", "prompt": "Speed benchmark comparison across models", "src": "/images/carousel/benchmarks/image-3.jpg", "alt": "Speed and efficiency benchmark chart"}, {"title": "Accuracy measurements", "description": "Precision and accuracy metrics", "prompt": "Accuracy benchmark evaluation", "src": "/images/carousel/benchmarks/image-4.jpg", "alt": "Accuracy measurement benchmark results"}]}]}, "stats": {"title": "Trusted by Millions", "subtitle": "Join the growing community of creators, developers, and businesses using Gemini 2.5 Flash Image to bring their ideas to life.", "items": {"imagesGenerated": {"number": "10M+", "label": "Images Generated", "description": "High-quality AI images created by users worldwide"}, "activeUsers": {"number": "500K+", "label": "Active Users", "description": "Creative professionals and enthusiasts using our platform"}, "uptime": {"number": "99.9%", "label": "Uptime", "description": "Reliable service with enterprise-grade infrastructure"}, "languages": {"number": "50+", "label": "Languages", "description": "Support for prompts in multiple languages worldwide"}}, "realTimeNote": "Real-time statistics updated every hour"}, "testimonials": {"title": "What Creators Say", "subtitle": "Hear from the creative professionals, developers, and businesses who are already transforming their workflows with Gemini 2.5 Flash Image.", "joinText": "Join", "satisfiedCreators": "satisfied creators", "items": [{"name": "<PERSON>", "role": "Creative Director", "company": "Design Studio Pro", "avatar": "👩‍🎨", "content": "Gemini 2.5 Flash Image has revolutionized our creative workflow. The quality and speed of image generation is incredible, and the ability to iterate quickly has saved us countless hours.", "rating": 5}, {"name": "<PERSON>", "role": "Marketing Manager", "company": "TechStart Inc", "avatar": "👨‍💼", "content": "We use this for all our marketing campaigns now. The consistency in character design and the ability to create variations instantly has been a game-changer for our brand.", "rating": 5}, {"name": "<PERSON>", "role": "Freelance Illustrator", "company": "Independent Artist", "avatar": "👩‍🎨", "content": "As a freelancer, this tool has expanded my capabilities tremendously. I can now offer services I never could before, and my clients are amazed by the results.", "rating": 5}, {"name": "<PERSON>", "role": "Product Designer", "company": "Innovation Labs", "avatar": "👨‍💻", "content": "The precision control and natural language interface make it incredibly intuitive. It's like having a creative partner that understands exactly what you're envisioning.", "rating": 5}, {"name": "<PERSON>", "role": "Content Creator", "company": "Social Media Agency", "avatar": "👩‍💻", "content": "Creating engaging visual content has never been easier. The variety of styles and the ability to maintain brand consistency across all our campaigns is outstanding.", "rating": 5}, {"name": "<PERSON>", "role": "Art Director", "company": "Creative Collective", "avatar": "👨‍🎨", "content": "Benchmark‑resultaterne taler for sig selv, men det, der virkelig imponerede mig, er, hvordan den håndterer komplekse kreative briefs. Den genererer ikke bare billeder – den forstår den kreative intention."}]}, "tryGemini": {"title": "Prøv Gemini 2.5 Flash Image", "subtitle": "Klar til at opleve fremtiden for AI‑billedgenerering? Vælg din foretrukne platform og kom i gang på få minutter.", "options": [{"title": "Prøv i Gemini", "description": "Oplev Gemini 2.5 Flash Image direkte i Gemini‑appen med en brugervenlig grænseflade.", "url": "https://gemini.google.com", "buttonText": "Prøv i Gemini", "isPrimary": true}, {"title": "Prøv i Google AI Studio", "description": "Få adgang til avancerede funktioner og API‑muligheder for udviklere og power‑brugere.", "url": "https://aistudio.google.com", "buttonText": "Prøv i AI Studio", "isPrimary": false}], "additionalInfo": {"title": "Klar til at komme i gang?", "description": "Slut dig til millioner af brugere, der allerede skaber fantastiske billeder med Gemini 2.5 Flash Image. Uanset om du er kreativ professionel, ud<PERSON><PERSON> eller blot udforsker AI‑muligheder – der er en perfekt måde for dig at starte.", "features": ["<PERSON><PERSON><PERSON> at prøve", "Ingen installation nødvendig", "Begynd at skabe med det samme"]}}, "pages": {"index": {"title": "Gemini 2.5 Flash Image: Næste generations AI‑billedgenerator – Karakterkonsistens og samtalebaseret redigering", "description": "Oplev de revolutionerende muligheder for AI‑billedgenerering i Gemini 2.5 Flash Image: brancheførende karakterkonsistens, samtalebaseret redigering og multi‑billedfusion. Komplet udviklervejledning, API‑integration og praktiske eksempler."}, "geminiVsImagen": {"title": "Gemini 2.5 Flash Image vs Imagen 4: 2024’s bedste sammenligning af AI‑billedgeneratorer", "description": "Dybdegående sammenligning af Gemini 2.5 Flash Image vs Imagen 4 inden for karakterkonsistens, teks<PERSON>ndering, stiloverførsel og andre k<PERSON>, så du kan vælge det bedste værktøj."}, "nanoBananaTruth": {"title": "Sandheden om ‘nano‑banana’: Hvorfor er Gemini 2.5 Flash Image så eftertragtet?", "description": "Dyk ned i det mystiske kodenavn ‘nano‑banana’ bag Gemini 2.5 Flash Image, hele rejsen fra betatest til officiel lancering og reel analyse af community‑feedback."}, "developerGuide": {"title": "Udviklerguide – Integration af Gemini 2.5 Flash Image API", "description": "Komplet udviklerguide til integration af Gemini 2.5 Flash Image API. Kodeeksempler, best practices og implementeringsvejledninger.", "hero": {"title": "Udviklerguide", "description": "<PERSON><PERSON><PERSON> at integrere Gemini 2.5 Flash Image i dine applikationer. <PERSON>a grundlæggende opsætning til avancerede funktioner – alt, hvad du behøver, er her.", "tags": {"apiIntegration": "API‑integration", "codeExamples": "Kodeeksempler", "bestPractices": "Best Practices", "practicalTutorials": "Praktiske tutorials"}}, "quickStart": {"title": "Hurtig start", "steps": {"getApiKey": {"title": "1. Hent <PERSON>‑nøgle", "description": "<PERSON><PERSON><PERSON> skal du hente en API‑nøgle fra Google AI Studio for at få adgang til Gemini 2.5 Flash Image."}, "installSdk": {"title": "2. Installer SDK"}, "basicUsage": {"title": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> brugseksempel", "codeComment": "<PERSON><PERSON><PERSON><PERSON><PERSON> om nødvendigt", "prompt": "Generér et billede af en bjergsolnedgang"}}}, "resources": {"title": "Flere ressourcer", "description": "Udforsk flere værktøjer og ressourcer for at få mest muligt ud af Gemini 2.5 Flash Image", "modelComparison": {"title": "Modelsammenligningsanalyse", "description": "<PERSON><PERSON> ned i, hvordan Gemini 2.5 Flash Image sammenlignes med andre <PERSON>‑billedgenereringsmodeller"}, "nanoBananaStory": {"title": "‘nano‑banana’-historien", "description": "Få hele historien om Gemini 2.5 Flash Image – fra mystisk kodenavn til officiel lancering"}}}}, "comparison": {"hero": {"title": "Gemini 2.5 Flash Image vs. Imagen 4", "subtitle": "2024's mest autoritative sammenligning af AI-billedgenereringsmodeller – fra figurkonsistens til tekstgengivelse, fra omkostningseffektivitet til praktiske anvendelser; en fuld analyse af de to giganters styrker og svagheder", "tags": {"characterConsistency": "Sammenligning af figurkonsistens", "textRendering": "Tekstgengivelse", "costAnalysis": "Omkostningseffektivitet", "practicalTests": "Praktiske testresultater"}}, "table": {"title": "Oversigt over kernekompetencer", "headers": {"capability": "Kompetence", "gemini": "Gemini 2.5 Flash Image", "imagen": "Imagen 4", "winner": "<PERSON><PERSON>"}, "rows": {"characterConsistency": {"name": "Figurkonsistens", "geminiRating": "⭐⭐⭐⭐⭐ <PERSON>ens førende", "imagenRating": "⭐⭐⭐⭐ God ydeevne", "winner": "Gemini 2.5"}, "conversationalEditing": {"name": "Samtale<PERSON>ret redigering", "geminiRating": "⭐⭐⭐⭐⭐ Fremragende", "imagenRating": "⭐⭐ Middel", "winner": "Gemini 2.5"}, "textRendering": {"name": "Tekstgengivelse", "geminiRating": "⭐⭐⭐⭐ God", "imagenRating": "⭐⭐⭐⭐⭐ Fremragende", "winner": "Imagen 4"}, "generationSpeed": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "geminiRating": "⭐⭐⭐⭐⭐ <PERSON><PERSON> hurtig", "imagenRating": "⭐⭐⭐ Middel", "winner": "Gemini 2.5"}, "costEffectiveness": {"name": "Omkostningseffektivitet", "geminiRating": "⭐⭐⭐⭐⭐ <PERSON><PERSON> p<PERSON>g", "imagenRating": "⭐⭐⭐ Middel", "winner": "Gemini 2.5"}, "multiImageFusion": {"name": "Fletning af flere billeder", "geminiRating": "⭐⭐⭐⭐⭐ Fremragende", "imagenRating": "⭐⭐⭐ God", "winner": "Gemini 2.5"}, "styleTransfer": {"name": "Stiloverførsel", "geminiRating": "⭐⭐⭐⭐ God", "imagenRating": "⭐⭐⭐⭐⭐ Fremragende", "winner": "Imagen 4"}, "worldKnowledge": {"name": "Verdensviden", "geminiRating": "⭐⭐⭐⭐⭐ Fremragende", "imagenRating": "⭐⭐⭐ God", "winner": "Gemini 2.5"}, "censorship": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "geminiRating": "⭐⭐⭐ Moderat", "imagenRating": "⭐⭐⭐⭐ Strammere", "winner": "Balance<PERSON>"}}}, "detailedAnalysis": {"title": "Detaljeret analyse", "geminiStrengths": {"title": "Styrker ved Gemini 2.5 Flash Image", "characterConsistency": {"title": "Branchens førende figurkonsistens", "description": "Uovertruffen evne til at bevare karaktertræk på tværs af flere billeder"}, "conversationalEditing": {"title": "Samtalebaseret redigeringsoplevelse", "description": "<PERSON>fin billeder gradvist gennem naturlig sprogdialog"}, "multiImageFusion": {"title": "<PERSON><PERSON><PERSON> til at flette flere billeder", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> flere <PERSON> intelligent"}, "freeToUse": {"title": "Grat<PERSON> at bruge", "description": "<PERSON><PERSON><PERSON> adgang via Gemini-appen"}}, "imagenStrengths": {"title": "Styrker ved Imagen 4", "textRendering": {"title": "Fremragende tekstgengivelse", "description": "Gengiver tekst og logoer nøjagtigt i billeder"}, "styleTransfer": {"title": "<PERSON><PERSON><PERSON> s<PERSON>førsel", "description": "<PERSON><PERSON><PERSON><PERSON> kont<PERSON> over kunstneriske stilarter og visuelle effekter"}, "worldKnowledge": {"title": "Bedre verdensviden", "description": "Mere præcis repræsentation af virkelige koncepter"}, "balancedModeration": {"title": "Balanceret moderering", "description": "Moderat indholdsfiltrering uden overbegrænsning"}}}, "conclusion": {"title": "Konklusion", "summary": "Både Gemini 2.5 Flash Image og Imagen 4 har deres styrker. Gemini excellerer i figurkonsistens og samtalebaseret redigering, mens Imagen 4 fører an i tekstgengivelse og stiloverførsel.", "recommendation": "Det bedste valg afhænger af dine specif<PERSON><PERSON> behov, budget og projektkrav. For de fleste kreative projekter anbefaler vi at starte med gratis Gemini 2.5 Flash Image og derefter overveje Imagen 4's professionelle funktioner efter behov."}}, "limitations": {"title": "Nuv<PERSON><PERSON><PERSON>r<PERSON><PERSON>", "subtitle": "Forstå grænserne for AI-billedgenereringsteknologi", "items": {"0": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "AI-genererede billeder afspejler ikke altid virkelige forhold eller aktuelle begivenheder nøjagtigt. Verificér altid vigtig information uafhængigt."}, "1": {"title": "Figurkonsistens", "description": "Selv om det er markant forbedret, kan perfekt konsistens på tværs af komplekse scenarier stadig være udfordrende i visse grænsetilfælde."}}, "note": "Disse begrænsninger adresseres aktivt gennem løbende forskning og udvikling. Y<PERSON>sen forbedres løbende med hver opdatering."}, "safety": {"title": "Sikkerhed og ansvarlighed", "subtitle": "Vi er forpligtet til ansvarlig AI‑udvikling med omfattende sikkerhedsforanstaltninger, etiske retningslinjer og brugerbeskyttelse som kernen i vores billedgenereringsteknologi.", "items": {"0": {"title": "Indholdssikkerhed", "description": "Avancerede filtreringssystemer forhindrer generering af skadeligt, upassende eller farligt indhold samtidig med at den kreative frihed bevares."}, "1": {"title": "Etisk AI", "description": "<PERSON><PERSON><PERSON> på principper for ansvarlig AI, der sikrer fair repræsentation og undgår bias i genereret indhold."}, "2": {"title": "Beskyttelse af privatliv", "description": "Dine billeder og prompts behandles med respekt for privatlivet i overensstemmelse med Googles strenge standarder for privatliv og databeskyttelse."}}, "commitment": {"title": "Forpligtelse til ansvarlig AI", "description": "Vi er forpligtet til at udvikle AI ansvarligt. Gemini 2.5 Flash Image gennemgår grundig test og evaluering for at sikre, at det lever op til vores høje standarder for sikkerhed, retfærdighed og pålidelighed.", "learnMore": "<PERSON><PERSON>s mere om Googles AI‑principper"}}, "nanoBanana": {"hero": {"title": "Nano-banana‑sandheden", "subtitle": "Oplev den fascinerende rejse bag det mystiske kodenavn, der fangede AI‑fællesskabets fantasi og blev en legende i maskinlæringens historie.", "tags": {"betaLegend": "Beta‑legende", "communityBuzz": "Fællesskabssnak", "censorshipControversy": "Censurkontrovers"}}, "timeline": {"title": "Nano-banana‑tidslinjen", "phases": {"internalTesting": {"period": "Starten af 2024", "title": "Intern testfase", "description": "Googles interne teams begyndte at teste de banebrydende billedgenereringsmuligheder under kodenavnet ‘nano-banana’ og holdt projektet strengt fortroligt."}, "communityDiscovery": {"period": "Midten af 2024", "title": "Fællesskabsopdagelse", "description": "AI‑forskere og entusiaster fandt referencer til ‘nano-banana’ i API‑svar og begyndte at spekulere over dets kapabiliteter på Reddit og Twitter."}, "officialRelease": {"period": "Slutningen af 2024", "title": "Officiel lancering", "description": "Google annoncerede officielt Gemini 2.5 Flash Image og afslørede, at ‘nano-banana’ var det interne kodenavn for denne banebrydende teknologi."}, "communityConcerns": {"period": "<PERSON>u", "title": "Bekymringer i fællesskabet", "description": "Løbende diskussioner om indholdspolitikker, censur og balancen mellem kreativ frihed og ansvarlig AI‑udvikling."}}}, "communityImpact": {"title": "Indvirkning på fællesskabet", "betaGloryDays": {"title": "Glansdagene i beta", "redditBuzz": {"title": "Reddit‑omtale", "description": "Fællesskaberne r/MachineLearning og r/artificial roste nano-banana’s evner"}, "lmArenaRankings": {"title": "LMArena‑placeringer i top", "description": "<PERSON><PERSON><PERSON> hurt<PERSON>t til tops i billedgenereringsopgaver"}, "developerAdoption": {"title": "Adoption i udviklerfællesskabet", "description": "De tidlige adoptører begyndte at integrere det i forskellige applikationer"}}, "officialControversy": {"title": "Kontrovers ved officiel udgivelse", "overModeration": {"title": "Overmoderering", "description": "<PERSON><PERSON> prompts, der virkede i beta, blev afvist i den officielle udgivelse"}, "communityDisappointment": {"title": "Skuffelse i fællesskabet", "description": "Brugere udtrykte utilfredshed med begrænsningerne i den officielle version"}, "featureRegression": {"title": "Funktionstilbagegang", "description": "Nogle avancerede funktioner klarede sig dårligere i den officielle udgivelse end i beta"}}}, "futureOutlook": {"title": "Fremtidsudsigter", "description": "P<PERSON> trods af en vis kontrovers ved den officielle lancering repræsenterer nano-banana (nu Gemini 2.5 Flash Image) stadig et stort gennembrud inden for AI‑billedgenerering. Google indsamler aktivt brugerfeedback og forbedrer løbende modellen.", "shortTerm": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> for<PERSON><PERSON>er", "optimizeModeration": "Optimér modereringsmekanismer", "improveQuality": "<PERSON><PERSON><PERSON>", "enhanceStability": "Øg stabiliteten"}, "longTerm": {"title": "Langsigtet vision", "strongerCreativity": "Stærkere kreative evner", "betterExperience": "<PERSON>re brugeroplev<PERSON>", "broaderApplications": "Bredere anvendel<PERSON>områ<PERSON>"}}, "callToAction": {"title": "<PERSON>lev nano-banana‑legenden", "description": "Prøv Gemini 2.5 Flash Image nu, og oplev de kraftfulde muligheder i den tidligere mystiske nano-banana.", "tryInGemini": "Prøv i Gemini", "tryInAiStudio": "Prøv i AI Studio"}}, "footer": {"quickLinks": "Hurtige links", "resources": "<PERSON><PERSON><PERSON><PERSON>", "features": "<PERSON><PERSON><PERSON>", "testimonials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modelComparison": "<PERSON><PERSON><PERSON><PERSON><PERSON>ng", "about": "Om", "pricing": "<PERSON><PERSON><PERSON>", "contact": "Kontakt", "helpCenter": "Hjælpcenter", "tutorials": "Vejledninger", "apiDocs": "API‑dokumentation", "community": "Fællesskab", "copyright": "Alle rettigheder forbeholdes.", "privacyPolicy": "Privatlivspolitik", "termsOfService": "Servicevilkår", "cookiePolicy": "Cookiepolitik"}, "privacyPolicy": {"title": "Privatlivspolitik", "subtitle": "Hvordan vi indsamler, bruger og beskytter dine oplysninger", "lastUpdated": "Senest opdateret: december 2024", "sections": {"introduction": {"title": "Introduktion", "content": "Hos Flash Image Fun værner vi om dit privatliv og sikrer dine personoplysninger. Denne privatlivspolitik forklarer, hvordan vi indsamler, bruger, videregiver og beskytter dine oplysninger, når du bruger vores AI‑billedgenereringstjeneste."}, "informationCollection": {"title": "Oplysninger vi indsamler", "content": "<PERSON>i inds<PERSON><PERSON> oply<PERSON>, som du giver direkte til os, f.eks. når du opretter en konto, bruger vores tjenester eller kontakter os for support. Dette kan omfatte din e‑mailadresse, brugsdata samt de billeder og prompts, du indsender til vores tjeneste."}, "howWeUse": {"title": "Hvordan vi bruger dine oplysninger", "content": "Vi bruger de indsamlede oplysninger til at levere, vedligeholde og forbedre vores tjenester, behandle dine foresp<PERSON>rgsler, kommunikere med dig og sikre vores platforms sikkerhed og integritet."}, "dataSharing": {"title": "<PERSON><PERSON> af oply<PERSON>ninger", "content": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, bytter eller overfører ikke dine personoplysninger til tredjeparter uden dit samtykke, medmindre det er beskrevet i denne politik eller krævet ved lov."}, "dataSecurity": {"title": "Datasikkerhed", "content": "Vi implementerer passende tekniske og organisatoriske foranstaltninger for at beskytte dine personoplysninger mod uautoriseret adgang, æ<PERSON><PERSON>, offentliggørelse eller destruktion."}, "yourRights": {"title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "content": "Du har ret til at få adgang til, op<PERSON>re eller slette dine personoplysninger. Du kan også fravælge visse former for kommunikation fra os."}, "contact": {"title": "Kontakt os", "content": "<PERSON><PERSON> du har spørgsmål til denne privatlivspolitik, kan du kontakte os på <EMAIL>"}}}, "termsOfService": {"title": "Servicevilkår", "subtitle": "Vilkår og betingelser for brug af vores tjeneste", "lastUpdated": "Senest opdateret: december 2024", "sections": {"acceptance": {"title": "Accept af vilkår", "content": "Ved at tilgå og bruge Flash Image Fun accepterer du at være bundet af vilkårene og bestemmelserne i denne aftale."}, "serviceDescription": {"title": "Beskrivelse af tjenesten", "content": "Flash Image Fun tilbyder AI‑drevet billedgenerering ved hjælp af avancerede maskinlæringsmodeller. Vores tjeneste giver brugere mulighed for at skabe, redigere og forbedre billeder via tekstprompter og andre input."}, "userResponsibilities": {"title": "Brugeransvar", "content": "Du er ansvarlig for din brug af tjenesten og for alt indhold, du opretter eller deler. Du må ikke bruge tjenesten til ulovlige formål eller på en måde, der kan skade vores tjeneste eller andre brugere."}, "contentPolicy": {"title": "Indholdspolitik", "content": "Du må ikke bruge vores tjeneste til at skabe indhold, der er ulovligt, skadel<PERSON><PERSON>, truende, chikanerende, injurierende eller på anden måde anstødeligt. Vi forbeholder os retten til at fjerne indhold, der overtræder vores politikker."}, "intellectualProperty": {"title": "Intellektuelle rettigheder", "content": "Du bevarer ejerskabet af det indhold, du skaber med vores tjeneste. Du giver os dog en licens til at bruge, lagre og behandle dit indhold i det omfang, det er nødvendigt for at levere vores tjenester."}, "limitation": {"title": "Ansvarsbegrænsning", "content": "Vores tjeneste leveres ‘som den er’ uden nogen form for garanti. Vi er ikke ansvarlige for skader, der opstår som følge af din brug af tjenesten."}, "termination": {"title": "Opsigelse", "content": "Vi kan til enhver tid afbryde eller suspendere din adgang til tjenesten, med eller uden grund og med eller uden varsel."}, "contact": {"title": "Kontaktoplysninger", "content": "<PERSON><PERSON> du har spørgsmål til disse servicevilkår, kan du kontakte os på <EMAIL>"}}}, "cookiePolicy": {"title": "Cookiepolitik", "subtitle": "Hvordan vi bruger cookies og lignende teknologier", "lastUpdated": "Senest opdateret: december 2024", "sections": {"whatAreCookies": {"title": "Hvad er cookies", "content": "Cookies er små tekstfiler, der gemmes på din enhed, når du besøger vores website. De hjæ<PERSON>per os med at give dig en bedre oplevelse ved at huske dine præferencer og forbedre vores tjeneste."}, "howWeUseCookies": {"title": "Hvordan vi bruger cookies", "content": "Vi bruger cookies til at forbedre din browsing‑oplevelse, analysere trafik på websitet, personalisere indhold og huske dine præferencer. Vi bruger også cookies til sikkerhed og for at forebygge svig."}, "typesOfCookies": {"title": "Typer af cookies vi bruger", "content": "Vi bruger nødvendige cookies (for at websitet kan fungere), performancookies (til at analysere, hvordan du bruger siden) og funktionscookies (til at huske dine præferencer)."}, "managingCookies": {"title": "Administration af cookies", "content": "Du kan styre og administrere cookies via dine browserindstillinger. Visse funktioner på websitet kan dog blive påvirket, hvis du deaktiverer cookies."}, "thirdPartyCookies": {"title": "Tredjepartscookies", "content": "Vi kan anvende tredje<PERSON>, der sætter cookies på vores vegne. Disse cookies er underlagt de respektive tredjepartes privatlivspolitikker."}, "contact": {"title": "Kontakt os", "content": "<PERSON><PERSON> du har spørgsm<PERSON><PERSON> til vores brug af cookies, kan du kontakte os på <EMAIL>"}}}}