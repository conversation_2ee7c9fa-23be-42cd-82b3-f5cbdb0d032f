/**
 * 动态多语言系统
 * 基于统一的语言配置，支持动态加载翻译
 */

import {
  getEnabledLanguages,
  DEFAULT_LANGUAGE
} from '../config/languages';

// 导入基础翻译文件
import enTranslations from './en.json';

// 导出语言配置 - 直接使用统一配置
export const languages = getEnabledLanguages().reduce((acc, lang) => {
  acc[lang.code] = lang.name;
  return acc;
}, {} as Record<string, string>);

// 导出语言代码数组
export const languageCodes = getEnabledLanguages().map(lang => lang.code);

export const defaultLang = DEFAULT_LANGUAGE;

// 动态导入所有翻译文件
const translationModules = import.meta.glob('./*.json');

// 初始化翻译对象，先加载英文作为基础
export const translations: Record<string, any> = {
  en: enTranslations,
};

// 动态加载其他翻译文件
export async function loadTranslation(langCode: string): Promise<any> {
  if (translations[langCode]) {
    return translations[langCode];
  }

  try {
    const modulePath = `./${langCode}.json`;
    if (translationModules[modulePath]) {
      const module = await translationModules[modulePath]();
      translations[langCode] = (module as any).default;
      return translations[langCode];
    }
  } catch (error) {
    console.warn(`Failed to load translation for ${langCode}:`, error);
  }

  // 回退到英文
  return translations[defaultLang];
}

// 预加载所有可用的翻译文件
export async function preloadAllTranslations(): Promise<void> {
  const loadPromises = Object.keys(translationModules).map(async (path) => {
    const langCode = path.match(/\.\/(.+)\.json$/)?.[1];
    if (langCode && langCode !== 'en') {
      try {
        const module = await translationModules[path]();
        translations[langCode] = (module as any).default;
      } catch (error) {
        console.warn(`Failed to preload translation for ${langCode}:`, error);
      }
    }
  });

  await Promise.all(loadPromises);
}

// 支持动态路由的语言检测
export function getLangFromUrl(url: URL) {
  const [, lang] = url.pathname.split('/');
  if (lang in languages) return lang as keyof typeof languages;
  return defaultLang;
}

// 验证语言代码是否有效
export function isValidLang(lang: string): lang is keyof typeof languages {
  return lang in languages;
}

// 生成本地化路径
export function getLocalizedPath(path: string, lang: keyof typeof languages) {
  // 移除开头的斜杠
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  // 如果是根路径，只返回语言代码
  if (!cleanPath || cleanPath === '') {
    return `/${lang}`;
  }
  // 返回带语言前缀的路径
  return `/${lang}/${cleanPath}`;
}

// 从路径中移除语言前缀
export function removeLanguagePrefix(path: string) {
  const segments = path.split('/').filter(Boolean);
  if (segments.length > 0 && isValidLang(segments[0])) {
    return '/' + segments.slice(1).join('/');
  }
  return path;
}

export function useTranslations(lang: keyof typeof languages) {
  return function t(key: string) {
    const translation = translations[lang as string];
    
    // Try to get the value from the current language
    let value = translation ? key.split('.').reduce((obj: any, k) => obj && obj[k], translation) : null;
    
    // If not found and not default language, fallback to default language
    if (!value && lang !== defaultLang) {
      const defaultTranslation = translations[defaultLang];
      value = defaultTranslation ? key.split('.').reduce((obj: any, k) => obj && obj[k], defaultTranslation) : null;
    }
    
    return value || key;
  }
}

// 异步版本的翻译函数，支持动态加载
export async function useTranslationsAsync(lang: keyof typeof languages) {
  // 确保翻译文件已加载
  await loadTranslation(lang as string);
  
  return function t(key: string) {
    const translation = translations[lang as string];
    
    // Try to get the value from the current language
    let value = translation ? key.split('.').reduce((obj: any, k) => obj && obj[k], translation) : null;
    
    // If not found and not default language, fallback to default language
    if (!value && lang !== defaultLang) {
      const defaultTranslation = translations[defaultLang];
      value = defaultTranslation ? key.split('.').reduce((obj: any, k) => obj && obj[k], defaultTranslation) : null;
    }
    
    return value || key;
  }
}

// 检查翻译文件是否已加载
export function isTranslationLoaded(langCode: string): boolean {
  return !!translations[langCode];
}

// 获取已加载的语言列表
export function getLoadedLanguages(): string[] {
  return Object.keys(translations);
}